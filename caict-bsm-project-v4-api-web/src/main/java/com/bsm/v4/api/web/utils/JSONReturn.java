package com.bsm.v4.api.web.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bsm.v4.system.model.contrust.system.ResponseCodeConst;


/**
 * <AUTHOR>
 * @date 2024/11/12 9:27
 * @description: JSON返回类
 */
public class JSONReturn {


    public JSONReturn() {
    }

    public static String serializerFeature(Object data) {
        return JSON.toJSONString(data, new SerializerFeature[]{SerializerFeature.DisableCircularReferenceDetect});
    }

    public static JSONObject getJson(boolean result, Object data, String msg) {
        JSONObject json = new JSONObject();
        json.put("success", result);
        json.put("message", msg);
        json.put("code", ResponseCodeConst.NORMAL.getType());
        json.put("version", 1.0F);
        json.put("data", JSONObject.toJSON(data));
        return json;
    }

    public static JSONObject getSuccessJson(Object data, Object single, String msg) {
        JSONObject json = new JSONObject();
        json.put("success", true);
        json.put("message", msg);
        json.put("version", 1.0F);
        json.put("data", JSONObject.toJSON(data));
        json.put("single", JSONObject.toJSON(single));
        return json;
    }

    public static JSONObject getSuccessJson(Object data, String msg) {
        return getJson(true, data, msg);
    }

    public static JSONObject getSuccessJson(Object data) {
        JSONObject json = new JSONObject();
        json.put("success", true);
        json.put("message", "");
        json.put("code", ResponseCodeConst.NORMAL.getType());
        json.put("version", 1.0F);
        json.put("data", JSONObject.toJSON(data));
        return json;
    }

    public static JSONObject getPageSuccessJson(int page, int current, int rows, int total, Object data) {
        JSONObject json = new JSONObject();
        json.put("success", true);
        json.put("message", "");
        json.put("version", 1.0F);
        json.put("page", page);
        json.put("current", current);
        json.put("rows", rows);
        json.put("total", total);
        json.put("data", JSONObject.toJSON(data));
        return json;
    }

    public static JSONObject getSuccessJson(String msg) {
        JSONObject json = new JSONObject();
        json.put("success", true);
        json.put("message", msg);
        json.put("code", ResponseCodeConst.NORMAL.getType());
        json.put("version", 1.0F);
        json.put("data", "");
        return json;
    }

    public static JSONObject getFailureJson(String failMsg,String code) {
        JSONObject json = new JSONObject();
        json.put("success", false);
        json.put("message", failMsg);
        json.put("code", code);
        json.put("version", 1.0F);
        json.put("data", "");
        return json;
    }

    public static JSONObject getPageFailureJson(int page, int current, int rows, int total, String failMsg) {
        JSONObject json = new JSONObject();
        json.put("success", false);
        json.put("message", failMsg);
        json.put("version", 1.0F);
        json.put("page", page);
        json.put("current", current);
        json.put("rows", rows);
        json.put("total", total);
        json.put("data", "");
        return json;
    }

    public static JSONObject getSuccessJson(Object data, String failMsg,String code) {
        JSONObject json = new JSONObject();
        json.put("success", true);
        json.put("message", failMsg);
        json.put("code", code);
        json.put("version", 1.0F);
        json.put("data", data);
        return json;
    }

    public static JSONObject getFailureJson(Object data, String failMsg,String code) {
        JSONObject json = new JSONObject();
        json.put("success", false);
        json.put("message", failMsg);
        json.put("code", code);
        json.put("version", 1.0F);
        json.put("data", data);
        return json;
    }
}
