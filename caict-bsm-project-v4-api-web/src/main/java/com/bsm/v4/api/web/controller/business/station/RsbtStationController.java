package com.bsm.v4.api.web.controller.business.station;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.station.RsbtStationWebService;
import com.bsm.v4.system.model.vo.business.station.StationVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/apiWeb/station/rsbtStation")
@Api(value = "web端基站数据管理接口", tags = "web端基站数据管理接口")
public class RsbtStationController extends BasicController {

    @Autowired
    private RsbtStationWebService rsbtStationWebService;
    @ApiOperation(value = "分页查询基站数据", notes = "分页查询基站数据接口")
    @RequestMapping(value = "/findAllByWhere", method = RequestMethod.POST)
    public JSONObject findAllByWhere(@RequestBody StationVO vo, @RequestHeader("token")String token) {
        return this.basicReturnJson(vo, RsbtStationWebService.class, (param, service) -> rsbtStationWebService.findAllByWhere(vo,token));
    }
//
//    @ApiOperation(value = "查询详情DTO", notes = "查询详情DTO接口")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "guid", value = "基站guid", paramType = "path", dataType = "String")
//    })
//    @RequestMapping(value = "/findOneByGuid/{guid}", method = RequestMethod.GET)
//    public JSONObject findOneByGuid(@PathVariable("guid") String guid) {
//        return this.basicReturnJson(guid, RsbtStationWebService.class, (param, service) -> service.findOneByGuid(guid));
//    }
//
//    @ApiOperation(value = "区域排名", notes = "区域排名（list按照降序排列）")
//    @RequestMapping(value = "/regionStationNumSort", method = RequestMethod.GET)
//    public JSONObject selectRegionSortDTO() {
//        return this.basicReturnJson(RsbtStationWebService.class, (param, service) -> service.selectRegionSortDTO());
//    }
//
//    @ApiOperation(value = "申请、台站、组织数量统计", notes = "申请、台站、组织数量统计")
//    @RequestMapping(value = "countTotal", method = RequestMethod.GET)
//    public JSONObject countTotal() {
//        return this.basicReturnJson(RsbtStationWebService.class, (param, service) -> service.countTotal());
//    }
//
//    @ApiOperation(value = "获取新增台站", notes = "获取新增台站")
//    @RequestMapping(value = "queryNewInsertStation", method = RequestMethod.GET)
//    public JSONObject queryNewInsertStation() {
//        return this.basicReturnJson(RsbtStationWebService.class, (param, service) -> service.queryNewInsertStation());
//    }
//
//    @ApiOperation(value = "台站区域统计", notes = "台站区域统计")
//    @RequestMapping(value = "stationCountyStatistics", method = RequestMethod.GET)
//    public JSONObject stationCountyStatistics() {
//        return this.basicReturnJson(RsbtStationWebService.class, (param, service) -> service.stationCountyStatistics());
//    }
//
//    @ApiOperation(value = "查询台站分布", notes = "查询台站分布")
//    @RequestMapping(value = "/getDistributionStatistics", method = RequestMethod.GET)
//    public JSONObject getDistributionStatistics() {
//        return this.basicReturnJson(RsbtStationWebService.class, (param, service) -> service.getDistributionStatistics());
//    }
//
//    @ApiOperation(value = "查询运营商基站数量", notes = "查询运营商基站数量（list按照降序排列）")
//    @RequestMapping(value = "/orgStationNumSort", method = RequestMethod.GET)
//    public JSONObject selectOrgStationNum() {
//        return this.basicReturnJson(RsbtStationWebService.class, (param, service) -> service.selectOrgStationNum());
//    }
//
//    @ApiOperation(value = "查询经纬度范围内的台站", notes = "查询经纬度范围内的台站")
//    @RequestMapping(value = "queryStationByRange", method = RequestMethod.POST)
//    public JSONObject queryStationByRange(@RequestBody List<PointDTO> pointDTOList) {
//        return this.basicReturnJson(pointDTOList, RsbtStationWebService.class, (param, service) -> service.queryStationByRange(pointDTOList));
//    }
//
//    @ApiOperation(value = "基站区域预警查询", notes = "基站区域预警查询")
//    @RequestMapping(value = "/queryAreaRange", method = RequestMethod.POST)
//    public JSONObject queryAreaRange(@RequestBody StationDTO stationDTO, HttpServletRequest request) {
//        Object accessToken = request.getHeader("token");
//        stationDTO.setToken(accessToken.toString());
//        return this.basicReturnJson(stationDTO, RsbtStationWebService.class, (param, service) -> service.queryAreaRange(stationDTO));
//    }

}
