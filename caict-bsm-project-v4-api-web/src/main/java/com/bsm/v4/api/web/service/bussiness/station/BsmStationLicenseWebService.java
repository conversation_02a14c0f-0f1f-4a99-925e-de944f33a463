package com.bsm.v4.api.web.service.bussiness.station;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.domain.security.service.business.license.LicenseService;
import com.bsm.v4.domain.security.service.business.license.LicenseTService;
import com.bsm.v4.domain.security.service.business.license.RuleCodeService;
import com.bsm.v4.domain.security.service.business.station.RsbtFreqService;
import com.bsm.v4.domain.security.service.business.station.RsbtStationService;
import com.bsm.v4.domain.security.service.security.OrgService;
import com.bsm.v4.domain.security.service.security.RegionService;
import com.bsm.v4.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.bsm.v4.system.model.dto.business.station.StationLicenseDTO;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.dto.security.RegionDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import com.bsm.v4.system.model.entity.business.license.RsbtLicenseT;
import com.bsm.v4.system.model.entity.business.license.RuleCode;
import com.bsm.v4.system.model.entity.business.station.RsbtFreq;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.utils.service.RedisService;
import com.caictframework.utils.util.VerificationCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BsmStationLicenseWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(BsmStationLicenseWebService.class);

    @Autowired
    private RsbtStationService rsbtStationService;
    @Autowired
    private RuleCodeService ruleCodeService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private LicenseTService licenseTService;
    @Autowired
    private RsbtFreqService rsbtFreqService;
    @Autowired
    private RegionService fsaRegionService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private RedisService redisService;

    /**
     * 生成执照
     */
    public void bsmStationLicense() {
        List<StationLicenseDTO> rsbtStationList = rsbtStationService.selectAllNotLicense();
        if (rsbtStationList.size() > 0) {
            List<OrgDTO> orgDTOS = orgService.findAllOrg(null);
            //开始生成执照编号，这里考虑数据量过大时，避免重复查询数据库
            List<RuleCode> ruleList = ruleCodeService.findAll();
            Map<String, RuleCode> ruleMap = ruleList.stream().collect(Collectors.toMap(RuleCode::getRuleType, Function.identity()));
//            RuleCode rule = createFileNo(ruleMap);
//            num = rule.getNextNum();
            //按地区对集合进行分组，用于生成执照编号
            Map<String, List<StationLicenseDTO>> collect = rsbtStationList.stream().collect(Collectors.groupingBy(StationLicenseDTO::getRegionCode));

            for (Map.Entry<String, List<StationLicenseDTO>> entry : collect.entrySet()) {
                List<RsbtLicense> bsmStationLicenseList = new ArrayList<>();
                List<RsbtLicenseT> licenseTList = new ArrayList<>();
                String regionCode = entry.getKey();
                LOG.info(new Timestamp(System.currentTimeMillis()) + "生成执照操作地区：" + regionCode);
                //获取地区父级
                //RegionDTO parent = fsaRegionService.findParentOneByName(county);
                //if (parent != null && (parent.getName().equals("县") || parent.getName().equals("市辖区"))) {
                //var temp = fsaRegionService.findById(parent.getParentId());
                //BeanUtils.copyProperties(temp, parent);
                List<StationLicenseDTO> value = entry.getValue();
                RuleCode rule = createFileNo(ruleMap, regionCode);
                String areaCode = String.format("%04d", Integer.parseInt(rule.getRuleType())).substring(0, 4);
                int num = Integer.parseInt(String.valueOf(rule.getNextNum()));
                for (StationLicenseDTO rsbtStation : value) {
                    //if (num >= 500000) {
                    String guid = VerificationCode.myUUID();
                    num++;
                    RsbtLicense bsmStationLicense = new RsbtLicense();
                    RsbtLicenseT licenseT = new RsbtLicenseT();
                    bsmStationLicense.setGuid(guid);
                    licenseT.setGuid(guid);
                    Date current = new Date();
                    bsmStationLicense.setStationGuid(rsbtStation.getGuid());
                    //String code = applyTable.getApplytableCode() + String.format("/C%04d", lastLicenseCount);
                    String code = rule.getYear() + "S" + areaCode + "FB";
                    code = code + String.format("%07d", num);
                    bsmStationLicense.setLicenseCode(code);
                    bsmStationLicense.setStationGuid(rsbtStation.getGuid());
                    bsmStationLicense.setAppCode(rsbtStation.getAppCode());
                    bsmStationLicense.setStatTdi(rsbtStation.getStatTdi());
                    for (OrgDTO dto : orgDTOS) {
                        if (dto.getType().equals(rsbtStation.getOrgType())) {
                            bsmStationLicense.setLicenseOrgName(dto.getOrgName());
                            break;
                        }
                    }
                    bsmStationLicense.setLicenseDate(current);
                    bsmStationLicense.setLicenseDate(current);
                    bsmStationLicense.setLicenseDateB(current);
                    bsmStationLicense.setLicenseType("1");//纸质
                    bsmStationLicense.setLicenseManager("无线电管理局");
                    // 设置执照时间
                    checkLicenseDate(rsbtStation, bsmStationLicense, current);

                    licenseT.setLicenseState(1L);
                    licenseT.setGuid(bsmStationLicense.getGuid());
                    licenseT.setLicenseCounty(rsbtStation.getCounty());
                    licenseT.setTechType(rsbtStation.getTechType());
                    licenseT.setOrgType(rsbtStation.getOrgType());

                    if (num == 9999999) {
                        break;
                    }
//                      insert(bsmStationLicense);
                    bsmStationLicenseList.add(bsmStationLicense);
                    licenseTList.add(licenseT);
                        //修改基站执照状态为已发布
//                          rsbtStation.setIsLicense("1");
                    //}
                }
                //添加执照
                licenseService.insertBatch(bsmStationLicenseList);
                licenseTService.insertBatch(licenseTList);
//                updateStation(rsbtStationList);
                rule.setNextNum(Long.valueOf(num));
                ruleCodeService.update(rule);
                //}
            }
        }
    }

    /**
     * 获取执照有效期
     *
     * @param rsbtStation rsbtStation
     * @param license     license
     * @param current     current
     */
    private void checkLicenseDate(StationLicenseDTO rsbtStation, RsbtLicense license, Date current) {
        var checkRuleRedisJson = JSONObject.parseObject(redisService.get("Caict:checkRule:netTs"));
        var netTsJson = checkRuleRedisJson.getJSONObject(rsbtStation.getOrgType());
        if (netTsJson != null) {
            var netTs = rsbtStation.getTechType();

            var freqList = rsbtFreqService.findOneByStationGuid(rsbtStation.getGuid());
            //获取规则
            List<FsaCheckRuleDTO> fsaCheckRuleDTOList = JSONArray.parseArray(netTsJson.getString(netTs), FsaCheckRuleDTO.class);
            if (fsaCheckRuleDTOList != null && fsaCheckRuleDTOList.size() > 0) {
                for (RsbtFreq freq : freqList) {
                    for (FsaCheckRuleDTO fsaCheckRuleDTO : fsaCheckRuleDTOList) {
                        //发射频率下限
                        var freqEfb = freq.getFreqEfb();
                        //发射频率上限
                        var freqEfe = freq.getFreqEfe();
                        //接收频率下限
                        var freqRfb = freq.getFreqRfb();
                        //接收频率上限
                        var freqRfe = freq.getFreqRfe();
                        var cal = Calendar.getInstance();
                        // 设置日期
                        cal.add(Calendar.YEAR, 3);
                        license.setLicenseDateE(cal.getTime());
                        //有效期校验
                        if ((fsaCheckRuleDTO.getFreqEfb() <= freqEfb && fsaCheckRuleDTO.getFreqEfe() >= freqEfe) && (fsaCheckRuleDTO.getFreqRfb() <= freqRfb && fsaCheckRuleDTO.getFreqRfe() >= freqRfe)) {
                            license.setLicenseDateB(current);
                            // 频率到期时间大于当前时间+3年, 使用当前时间+3年, 否则使用频率到期时间
                            if (fsaCheckRuleDTO.getEndDate().getTime() > cal.getTime().getTime()) {
                                license.setLicenseDateE(cal.getTime());
                            } else {
                                license.setLicenseDateE(fsaCheckRuleDTO.getEndDate());
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 批量修改基站表
     */
    public void updateStation(List<StationLicenseDTO> rsbtStationList) {
        rsbtStationService.updateStationLicenseStatus(rsbtStationList);
    }

    /**
     * 创建编号
     *
     * @return
     */
    public RuleCode createFileNo(Map<String, RuleCode> ruleMap, String regionCode) {
        RuleCode rule = ruleMap.get(regionCode);
        if (rule == null) {
            // 如果未找到对应规则，抛出异常
            throw new IllegalArgumentException("未找到对应规则: " + regionCode);
        }
        Long num = rule.getNextNum();
        Integer noYear = rule.getYear(); // 目前编号的年份
        Integer year = LocalDateTime.now().getYear(); // 当前年份
        if (year > noYear) {
            num = 5000000L;
            rule.setYear(year);
        }
        rule.setNextNum(num);
        return rule;
    }

}
