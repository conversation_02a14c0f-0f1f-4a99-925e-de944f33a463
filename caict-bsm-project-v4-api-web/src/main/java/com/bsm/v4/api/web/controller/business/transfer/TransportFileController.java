package com.bsm.v4.api.web.controller.business.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.TransportFileWebService;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: ycp
 * @createTime: 2023/09/11 16:21
 * @company: 成渝（成都）信息通信研究院
 * @description: 系统文件controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transfer/transportFile")
@Api(value = "web端文件管理接口", tags = "web端文件管理接口")
public class TransportFileController extends BasicController {

    @ApiOperation(value = "根据jobId查询文件分类详情", notes = "根据jobId查询文件分类详情接口")
    @GetMapping(value = "/findDetailByJobId/{jobId}/{fileType}")
    public JSONObject findDetailByJobId(@PathVariable("jobId") String jobId, @PathVariable("fileType") String fileType) {
        return this.basicReturnJson(jobId, TransportFileWebService.class, (vo, service) -> service.findFileDetailByJobId(vo, fileType));
    }
}
