package com.bsm.v4.api.web.service.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.domain.security.service.security.AuthService;
import com.bsm.v4.system.model.dto.security.LoginDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.security.Login;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.utils.util.MD5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * Created by dengsy on 2019-10-24.
 */
@Service
public class AuthWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(AuthWebService.class);

    @Autowired
    private AuthService authService;

    @Value("${caict.myPasswordKey}")
    private String myPasswordKey;

    @Autowired
    private UsersLogWebService usersLogWebService;

    /**
     * 登录
     */
    public Map<String, Object> loginOn(String loginName, String password, HttpServletRequest request) {
        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setLastIp(getIpAddr(request));
        loginDTO.setLoginName(loginName);
        loginDTO.setPassword(MD5Utils.getInstance().encode(password, myPasswordKey));
//        loginDTO.setPassword(password);
        Map<String, String> map = authService.loginOn(loginDTO);
        if (map != null) {
            usersLogWebService.insert(map.get("userId"), map.get("userName") + "登录系统", "system");
            Map<String, Object> stringObjectMap = this.basicReturnResultJson(map);
            return stringObjectMap;
        }
        return null;

    }


    /**
     * 获取登录用户的IP地址
     *
     * @param request HttpServletRequest
     * @return String
     */
    public static String getIpAddr(HttpServletRequest request) {
        if (request == null) {
            return "";
        }
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }
        if (ip.split(",").length > 1) {
            ip = ip.split(",")[0];
        }
        return ip;
    }

    /**
     * 登出
     */
    public Map<String, Object> loginOff(String token) {
        authService.loginOff(token);
        return this.basicReturnResultJson("登出成功");
    }

    /**
     * 根据token获取用户登陆信息json
     */
    public UsersDTO getLoginUsersDTO(String token) {
        String jsonString = this.authService.getLoginUsersDTO(token);
        return jsonString != null ? JSONObject.parseObject(jsonString,UsersDTO.class) : null;
    }

    public void setLogin(String key, String jsonString) {
        this.authService.setLogin(key, jsonString);
    }

    /**
     * 重置密码
     */
    public Map<String, Object> resetPassword(String loginName, String password) {
        authService.updatePasswordByLogin(loginName, MD5Utils.getInstance().encode(password, myPasswordKey));
        return this.basicReturnResultJson("重置成功");
    }

    /**
     * 根据token获取用户登陆信息
     */
    public UsersDTO findLoginUsersDTO(String token) {
        String jsonString = authService.getLoginUsersDTO(token);
        if (jsonString != null) {
            return JSONObject.parseObject(jsonString, UsersDTO.class);
        }
        return null;
    }

    /**
     * 修改密码
     *
     * @return
     */
    public Map<String, Object> updatePassword(String userId, String passwordOld, String passwordNew) {
        //判单旧密码是否正确
        Login login = authService.findOneByUserId(userId);
        if (authService.login(login.getLoginName(), MD5Utils.getInstance().encode(passwordOld, myPasswordKey)) != null) {
            //修改密码
            resetPassword(login.getLoginName(), passwordNew);
            //登录操作
            return loginOn(login.getLoginName(), passwordNew, null);
        }
        return null;
    }
}
