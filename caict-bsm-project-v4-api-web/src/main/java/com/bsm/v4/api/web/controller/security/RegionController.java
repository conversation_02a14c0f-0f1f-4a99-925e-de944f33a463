package com.bsm.v4.api.web.controller.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.security.RegionWebService;
import com.bsm.v4.system.model.vo.security.RegionVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * @Title: RegionController
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.controller.security
 * @Date 2023/8/16 17:23
 * @description:
 */
@RestController
@RequestMapping(value = "/apiWeb/security/region")
@Api(value = "web端-系统-省市区信息接口", tags = "web端-系统-省市区信息接口")
public class RegionController extends BasicController {

    @ApiOperation(value = "添加、修改省市区信息", notes = "添加、修改省市区信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "regionVO", value = "省市区对象", required = true, paramType = "body", dataType = "regionVO")
    })
    @PostMapping(value = "/")
    public JSONObject save(@RequestBody RegionVO regionVO) {
        return this.basicReturnJson(regionVO, RegionWebService.class, (vo, service) -> service.save(vo));

    }

    @ApiOperation(value = "查询全部省市区信息", notes = "查询全部省市区信息接口")
    @GetMapping(value = "/findAll")
    public JSONObject findAll() {
        return this.basicReturnJson(RegionWebService.class, (service) -> service.findAll());
    }

    @ApiOperation(value = "查询全部省市区信息（树形结构）", notes = "查询全部省市区信息（树形结构）接口")
    @GetMapping(value = "/findAllTree")
    public JSONObject findAllTree() {
        return this.basicReturnJson(RegionWebService.class, (service) -> service.findAllTree());
    }

    @ApiOperation(value = "查询一个省市区信息(根据id)", notes = "查询一个省市区信息(根据id)接口")
    @ApiImplicitParam(name = "id", value = "省市区id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findOneById/{id}")
    public JSONObject findOne(@PathVariable String id) {
        return this.basicReturnJson(id, RegionWebService.class, (vo, service) -> service.findOne(vo));

    }

    @ApiOperation(value = "查询一个省市区信息(根据code)", notes = "查询一个省市区信息(根据code)接口")
    @ApiImplicitParam(name = "code", value = "省市区code", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findOneByCode/{code}")
    public JSONObject findOneByCode(@PathVariable String code) {
        return this.basicReturnJson(code, RegionWebService.class, (vo, service) -> service.findOneCode(vo));
    }

    @ApiOperation(value = "查询所有的省市区信息（根据父级菜单Id)", notes = "查询所有的省市区信息（根据父级菜单Id)接口")
    @ApiImplicitParam(name = "parentId", value = "父级Id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findAllByParentId/{parentId}")
    public JSONObject findAllByParentId(@PathVariable String parentId) {
        return this.basicReturnJson(parentId, RegionWebService.class, (vo, service) -> service.findAllByParentId(vo));

    }

    @ApiOperation(value = "查询所有的省市区信息（根据父级菜单Id)(树形结构)", notes = "查询所有的省市区信息（根据父级菜单Id)(树形结构)接口")
    @ApiImplicitParam(name = "parentId", value = "父级id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findAllByParentIdTree/{parentId}")
    public JSONObject findAllByParentIdTree(@PathVariable String parentId) {
        return this.basicReturnJson(parentId, RegionWebService.class, (vo, service) -> service.findAllOfTreeByParentId(vo));

    }

    @ApiOperation(value = "查询所有的省市区信息（包含自身）（根据父级菜单Id)(树形结构)", notes = "查询所有的省市区信息（根据父级菜单Id)(树形结构)接口")
    @ApiImplicitParam(name = "parentId", value = "父级id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findAllTreeByIdOrParentId/{parentId}")
    public JSONObject findAllTreeByIdOrParentId(@PathVariable("parentId") String parentId) {
        return this.basicReturnJson(parentId, RegionWebService.class, (vo, service) -> service.findAllTreeByIdOrParentId(vo));
    }

    @ApiOperation(value = "查询所有的省市区信息（包含自身）", notes = "查询所有的省市区信息（包含自身）接口")
    @ApiImplicitParam(name = "parentId", value = "父级id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findAllByIdOrParentId/{parentId}")
    public JSONObject findAllByIdOrParentId(@PathVariable("parentId") String parentId) {
        return this.basicReturnJson(parentId, RegionWebService.class, (vo, service) -> service.findAllByIdOrParentId(vo));
    }

    @ApiOperation(value = "删除省市区信息", notes = "删除省市区信息接口")
    @ApiImplicitParam(name = "id", value = "省市区id", required = true, paramType = "path", dataType = "String")
    @DeleteMapping(value = "/delete/{id}")
    public JSONObject delete(@PathVariable String id) {
        return this.basicReturnJson(id, RegionWebService.class, (vo, service) -> service.delete(vo));

    }

    @ApiOperation(value = "根据市名字返回区", notes = "根据市名字返回区json接口")
    @RequestMapping(value = "/getAreaByProvince/{name}", method = RequestMethod.GET)
    public JSONObject getAreaByProvince(@PathVariable("name") String name) {
        return this.basicReturnJson(name, RegionWebService.class, (vo, service) -> service.getAreaByProvince(vo));
    }

    @ApiOperation(value = "查询登录账号市级数据", notes = "查询登录账号市级数据接口")
    @GetMapping(value = "/findAllCityByUsers")
    public JSONObject findAllCityByUsers(@RequestHeader("token") String token) {
        return this.basicReturnJson(token, RegionWebService.class, (vo, service) -> service.findAllCityByUsers(vo));

    }


}
