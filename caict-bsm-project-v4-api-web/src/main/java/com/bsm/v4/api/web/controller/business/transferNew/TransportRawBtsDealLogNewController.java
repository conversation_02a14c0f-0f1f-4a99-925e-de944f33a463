package com.bsm.v4.api.web.controller.business.transferNew;

import com.bsm.v4.api.web.service.bussiness.transferNew.TransportJobNewWebService;
import com.bsm.v4.api.web.service.bussiness.transferNew.TransportRawBtsDealLogNewWebService;
import com.bsm.v4.api.web.controller.config.BasicController;
import com.bsm.v4.api.web.utils.JSONReturn;
import com.bsm.v4.system.model.contrust.system.ResponseCodeConst;
import com.caictframework.utils.util.JSONResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jline.utils.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.domain.security.service.business.transferNew.TransportRawBtsDealLogNewService;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealLogNew;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 * 传输原始数据处理错误日志表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/transportRawBtsDealLogNew")
@Api(value = "web端传输原始数据处理错误日志管理接口", tags = "传输原始数据处理错误日志管理接口")
public class TransportRawBtsDealLogNewController extends BasicController {

    @Autowired
    private TransportRawBtsDealLogNewWebService transportRawBtsDealLogNewWebService;

    @Autowired
    private TransportRawBtsDealLogNewService transportRawBtsDealLogNewService;

    @ApiOperation(value = "错误日志下载", notes = "导出指定任务ID下的错误日志Excel文件并返回文件路径")
    @GetMapping("/exportErrorLog")
    public JSONObject exportErrorLog(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId) {
        
        // 参数验证
        if (jobId == null || jobId <= 0) {
            return JSONReturn.getFailureJson("", "任务ID不能为空且必须大于0", ResponseCodeConst.BAD_REQUEST.getType());
        }

        try {
            Map<String, Object> result = transportRawBtsDealLogNewWebService.exportErrorLog(jobId);
            
            // 根据Service层返回的结果判断成功或失败
            Boolean success = (Boolean) result.get("success");
            String message = (String) result.get("message");
            Object data = result.get("data");
            
            if (Boolean.TRUE.equals(success)) {
                return JSONReturn.getSuccessJson(data, message != null ? message : "导出成功", ResponseCodeConst.SUCCESS.getType());
            } else {
                return JSONReturn.getFailureJson(data, message != null ? message : "导出失败", ResponseCodeConst.BUG.getType());
            }
        } catch (IllegalArgumentException e) {
            return JSONReturn.getFailureJson("", e.getMessage(), ResponseCodeConst.BAD_REQUEST.getType());
        } catch (Exception e) {
            return JSONReturn.getFailureJson("", "导出失败: " + e.getMessage(), ResponseCodeConst.BUG.getType());
        }
    }
}