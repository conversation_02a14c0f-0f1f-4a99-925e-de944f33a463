package com.bsm.v4.api.web.config;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.caictframework.utils.util.JSONResult;
import java.io.PrintWriter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
/**
 * <AUTHOR>
 * @date 2025/7/16 12:00
 * @description:
 */


@Component
public class InterceptorConfig implements HandlerInterceptor {
    private static final Logger LOG = LoggerFactory.getLogger(InterceptorConfig.class);
    @Autowired
    private AuthWebService authWebService;

    public InterceptorConfig() {
    }

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
        Object accessToken = request.getHeader("token");
        if (accessToken != null) {
            UsersDTO usersDTO = this.authWebService.getLoginUsersDTO(accessToken.toString());
            if (usersDTO != null) {
                this.authWebService.setLogin("Caict:" + accessToken.toString(), JSONObject.toJSONString(usersDTO));
                return true;
            }
        }

        this.setResponseWrite(response, "-1");
        return false;
    }

    protected void setResponseWrite(HttpServletResponse response, String msg) throws Exception {
        response.reset();
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE, PUT");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, X-Requested-With, Content-Type, Accept ,token");
        PrintWriter pw = response.getWriter();
        JSONObject jsonObject = JSONResult.getFailureJson(msg);
        pw.write(jsonObject.toJSONString());
        pw.flush();
        pw.close();
    }

    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
    }

    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {
    }

}
