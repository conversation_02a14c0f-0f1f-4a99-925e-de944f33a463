package com.bsm.v4.api.web.controller.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.controller.config.BasicController;
import com.bsm.v4.api.web.service.security.UsersWebService;
import com.bsm.v4.system.model.vo.security.UsersSearchVO;
import com.bsm.v4.system.model.vo.security.UsersVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Title: UsersController
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.controller.security
 * @Date 2023/8/15 14:42
 * @description:
 */
@RestController
@RequestMapping(value = "/apiWeb/security/users")
@Api(value = "web端-系统-用户信息接口", tags = "web端-系统-用户信息接口")
public class UsersController extends BasicController {

    @ApiOperation(value = "register注册", notes = "register注册接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersVO", value = "用户对象", required = true, paramType = "body", dataType = "usersVO")
    })
    @PostMapping(value = "/register")
    public JSONObject register(@RequestBody UsersVO usersVO) {
        return this.basicReturnJson(usersVO, UsersWebService.class, (vo, service) -> service.register(vo));
    }

    @ApiOperation(value = "用户信息编辑保存", notes = "用户信息编辑保存接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersVO", value = "用户对象", required = true, paramType = "body", dataType = "usersVO")
    })
    @PostMapping(value = "/")
    public JSONObject save(@RequestBody UsersVO usersVO) {
        return this.basicReturnJson(usersVO, UsersWebService.class, (vo, service) -> service.save(vo));
    }

    @ApiOperation(value = "分页条件查询", notes = "分页条件查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersSearchVO", value = "用户信息搜索对象", required = true, paramType = "body", dataType = "usersSearchVO")
    })
    @PostMapping(value = "/findAllPageByWhere")
    public JSONObject findAllPageByWhere(@RequestBody UsersSearchVO usersSearchVO) {
        return this.basicReturnJson(usersSearchVO, UsersWebService.class, (vo, service) -> service.findAllPageByWhere(vo));
    }

    @ApiOperation(value = "根据id查询", notes = "根据id查询接口")
    @ApiImplicitParam(name = "id", value = "用户id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "findOne/{id}")
    public JSONObject findOne(@PathVariable("id") String id) {
        return this.basicReturnJson(id, UsersWebService.class, (vo, service) -> service.findOne(id));

    }

    @ApiOperation(value = "用户绑定角色", notes = "用户绑定角色接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "usersId", value = "用户id", required = true, dataType = "String")
    })
    @GetMapping(value = "/setUsersRole/{roleId}/{usersId}")
    public JSONObject setUsersRole(@PathVariable("roleId") String roleId, @PathVariable("usersId") String usersId) {
        return this.basicReturnJson(roleId, UsersWebService.class, (vo, service) -> service.setUsersRole(roleId, usersId));

    }

    @ApiOperation(value = "用户绑定组织机构", notes = "用户绑定组织机构接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgGuid", value = "组织机构id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "usersId", value = "用户id", required = true, dataType = "String")
    })
    @GetMapping(value = "/setUsersOrg/{orgGuid}/{usersId}")
    public JSONObject setUsersOrg(@PathVariable("orgGuid") String orgGuid, @PathVariable("usersId") String usersId) {
        return this.basicReturnJson(orgGuid, UsersWebService.class, (vo, service) -> service.setUsersOrg(orgGuid, usersId));
    }

    @ApiOperation(value = "删除用户信息", notes = "删除用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersId", value = "用户id", required = true, paramType = "path", dataType = "String")
    })
    @DeleteMapping(value = "/deleteUsers/{usersId}")
    public JSONObject deleteUsers(@PathVariable("usersId") String usersId) {
        return this.basicReturnJson(usersId, UsersWebService.class, (vo, service) -> service.delete(usersId));
    }

    @ApiOperation(value = "批量删除用户信息", notes = "批量删除用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersIds", value = "用户主键集合", allowMultiple = true, required = true, paramType = "body", dataType = "String")
    })
    @PostMapping(value = "/deletesUsers")
    public JSONObject deletesUsers(@RequestBody List<String> usersIds) {
        return this.basicReturnJson(usersIds, UsersWebService.class, (vo, service) -> service.deletesUsers(usersIds));
    }


}
