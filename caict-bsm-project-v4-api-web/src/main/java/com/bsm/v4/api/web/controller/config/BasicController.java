package com.bsm.v4.api.web.controller.config;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.utils.JSONReturn;
import com.bsm.v4.system.model.contrust.system.ResponseCodeConst;
import com.caictframework.utils.component.ApplicationContextProvider;
import com.caictframework.utils.util.JSONResult;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Iterator;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024/12/3 10:55
 * @description:
 */
public class BasicController {
    public BasicController() {
    }

    public JSONObject returnSuccess() {
        return this.returnSuccess("操作成功");
    }

    public JSONObject returnSuccess(String s) {
        return JSONResult.getSuccessJson(s);
    }

    public JSONObject returnFailure() {
        return this.returnFailure("操作失败");
    }

    public JSONObject returnFailure(String s) {
        return JSONResult.getFailureJson(s);
    }

    protected <R> JSONObject setReturn(R v) {
        Map m = (Map)v;
        String c = (String)m.get("code");
        String message = (String)m.get("message");
        Object data = m.get("data");
        
        if ("200".equals(c)) {
            return JSONReturn.getSuccessJson(data, message != null ? message : "操作成功", c);
        } else if("500".equals(c)){
            return JSONReturn.getFailureJson(data, message != null ? message : "系统异常", c);
        } else if("400".equals(c)){
            return JSONReturn.getFailureJson(data, message != null ? message : "请求参数错误", c);
        } else if("401".equals(c)){
            return JSONReturn.getFailureJson(data, message != null ? message : "未授权访问", c);
        } else if("403".equals(c)){
            return JSONReturn.getFailureJson(data, message != null ? message : "禁止访问", c);
        } else if("404".equals(c)){
            return JSONReturn.getFailureJson(data, message != null ? message : "资源不存在", c);
        } else if("409".equals(c)){
            return JSONReturn.getFailureJson(data, message != null ? message : "数据冲突", c);
        } else if("422".equals(c)){
            return JSONReturn.getFailureJson(data, message != null ? message : "数据校验失败", c);
        } else if("202".equals(c)){
            return JSONReturn.getSuccessJson(data, message != null ? message : "处理中", c);
        } else if("600".equals(c)){
            return JSONReturn.getFailureJson(data, message != null ? message : "登录超时", c);
        } else if("700".equals(c)){
            return JSONReturn.getSuccessJson(data, message != null ? message : "警告信息", c);
        } else {
            // 默认情况：根据success字段判断
            Boolean success = (Boolean)m.get("success");
            if (Boolean.FALSE.equals(success)) {
                return JSONReturn.getFailureJson(data, message != null ? message : "操作失败", c != null ? c : "500");
            } else {
                return JSONReturn.getSuccessJson(data, message != null ? message : "操作成功", c != null ? c : "200");
            }
        }
    }

    public <S, R> JSONObject basicReturnJson(Class<S> serviceClass, Function<S, R> func) {
        try {
            S service = ApplicationContextProvider.getBean(serviceClass);
            R v = func.apply(service);
            return this.setReturn(v);
        } catch (IllegalArgumentException e) {
            return JSONReturn.getFailureJson("", e.getMessage(), ResponseCodeConst.BAD_REQUEST.getType());
        } catch (SecurityException e) {
            return JSONReturn.getFailureJson("", e.getMessage(), ResponseCodeConst.UNAUTHORIZED.getType());
        } catch (Exception e) {
            return JSONReturn.getFailureJson("", "系统异常: " + e.getMessage(), ResponseCodeConst.BUG.getType());
        }
    }

    public <V, S, R> JSONObject basicReturnJson(V vo, Class<S> serviceClass, BiFunction<V, S, R> func) {
        try {
            S service = ApplicationContextProvider.getBean(serviceClass);
            R v = func.apply(vo, service);
            return this.setReturn(v);
        } catch (IllegalArgumentException e) {
            return JSONReturn.getFailureJson("", e.getMessage(), ResponseCodeConst.BAD_REQUEST.getType());
        } catch (SecurityException e) {
            return JSONReturn.getFailureJson("", e.getMessage(), ResponseCodeConst.UNAUTHORIZED.getType());
        } catch (Exception e) {
            return JSONReturn.getFailureJson("", "系统异常: " + e.getMessage(), ResponseCodeConst.BUG.getType());
        }
    }

    public <V, S, R, T> T basicReturn(V vo, Class<S> serviceClass, BiFunction<V, S, R> func) {
        S service = ApplicationContextProvider.getBean(serviceClass);
        R v = func.apply(vo, service);
        return (T) v;
    }

    public <V, U, A, S, R> String basicRedirect(V vo, U url, A a, Class<S> serviceClass, BiFunction<V, S, R> func) {
        S service = ApplicationContextProvider.getBean(serviceClass);
        R v = func.apply(vo, service);
        Map m = (Map)v;
        if (m != null) {
            Map<String, String> map = (Map)m.get("data");
            if (map != null) {
                RedirectAttributes redirectAttributes = (RedirectAttributes)a;
                Iterator var11 = map.keySet().iterator();

                while(var11.hasNext()) {
                    Object key = var11.next();
                    String k = (String)key;
                    redirectAttributes.addAttribute(k, map.get(k));
                }
            }
        }

        return "redirect:" + url;
    }
}
