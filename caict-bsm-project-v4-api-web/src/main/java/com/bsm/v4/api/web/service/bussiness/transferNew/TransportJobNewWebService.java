package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.api.web.service.bussiness.transfer.ApprovalTransportJobWebService;
import com.bsm.v4.api.web.service.bussiness.transfer.TransportJobWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.domain.security.service.business.rule.FsaCheckRuleService;
import com.bsm.v4.domain.security.service.business.transfer.AsyncRawBtsService;
import com.bsm.v4.domain.security.service.business.transfer.TransportScheduleService;
import com.bsm.v4.domain.security.service.business.transferNew.TransportJobNewService;
import com.bsm.v4.domain.security.service.business.transferNew.TransportRawBtsDealNewService;
import com.bsm.v4.domain.security.service.business.transferNew.TransportRawBtsDealLogNewService;
import com.bsm.v4.domain.security.service.business.transfer.TransportFileService;
import com.bsm.v4.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.bsm.v4.system.model.dto.business.transferNew.*;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalTransportJob;
import com.bsm.v4.system.model.entity.business.transfer.TransportFile;
import com.bsm.v4.system.model.vo.business.transferNew.*;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportSchedule;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobNew;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealNew;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealLogNew;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.service.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSON;
import com.bsm.v4.system.model.dto.security.RegionDTO;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobBranchNew;
import com.bsm.v4.domain.security.service.business.transferNew.TransportJobBranchNewService;

import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.Objects;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.document.HdfsWebService;

import javax.annotation.Resource;

/**
 * 传输任务表WebService
 */
@Service
public class TransportJobNewWebService extends BasicWebService {

    @Value("${caict.myFilePath}")
    private String myFilePath;

    @Autowired
    private TransportJobNewService transportJobNewService;

    @Autowired
    private TransportRawBtsDealNewService transportRawBtsDealNewService;

    @Autowired
    private TransportRawBtsDealLogNewService transportRawBtsDealLogNewService;

    @Autowired
    private TransportFileService transportFileService;

    @Autowired
    private AuthWebService authWebService;

    @Autowired
    private TransportJobBranchNewService transportJobBranchNewService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private FsaCheckRuleService fsaCheckRuleService;

    @Autowired
    private TransportScheduleService transportScheduleService;

    @Autowired
    private TransportJobWebService transportJobWebService;

    @Autowired
    private HdfsWebService hdfsWebService;

    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;

    @Autowired
    private AsyncRawBtsService asyncRawBtsService;

    @Resource
    ThreadPoolTaskExecutor asyncExecutor;

    // CSV字段映射配置：CSV表头 -> 实体类字段名
    private static final Map<String, String> CSV_FIELD_MAPPING = new LinkedHashMap<>();

    // 必填字段配置
    private static final Set<String> REQUIRED_FIELDS = new HashSet<>();

    // 数字字段配置
    private static final Set<String> NUMERIC_FIELDS = new HashSet<>();

    // 字符串字段长度限制配置
    private static final Map<String, Integer> STRING_LENGTH_LIMITS = new HashMap<>();

    static {
        // 初始化字段映射（根据TransportRawBtsDealNew实体类）
        CSV_FIELD_MAPPING.put("CELL_NAME", "cellName");
        CSV_FIELD_MAPPING.put("CELL_ID", "cellId");
        CSV_FIELD_MAPPING.put("BTS_NAME", "btsName");
        CSV_FIELD_MAPPING.put("BTS_ID", "btsId");
        CSV_FIELD_MAPPING.put("TECH_TYPE", "techType");
        CSV_FIELD_MAPPING.put("LOCATION", "location");
        CSV_FIELD_MAPPING.put("COUNTY", "county");
        CSV_FIELD_MAPPING.put("LONGITUDE", "longitude");
        CSV_FIELD_MAPPING.put("LATITUDE", "latitude");
        CSV_FIELD_MAPPING.put("SEND_START_FREQ", "sendStartFreq");
        CSV_FIELD_MAPPING.put("SEND_END_FREQ", "sendEndFreq");
        CSV_FIELD_MAPPING.put("ACC_START_FREQ", "accStartFreq");
        CSV_FIELD_MAPPING.put("ACC_END_FREQ", "accEndFreq");
        CSV_FIELD_MAPPING.put("MAX_EMISSIVE_POWER", "maxEmissivePower");
        CSV_FIELD_MAPPING.put("HEIGHT", "height");
        CSV_FIELD_MAPPING.put("DEVICE_FACTORY", "deviceFactory");
        CSV_FIELD_MAPPING.put("DEVICE_MODEL", "deviceModel");
        CSV_FIELD_MAPPING.put("MODEL_CODE", "modelCode");
        CSV_FIELD_MAPPING.put("ANTENNA_MODEL", "antennaModel");
        CSV_FIELD_MAPPING.put("ANTENNA_FACTORY", "antennaFactory");
        CSV_FIELD_MAPPING.put("POLARIZATION_MODE", "polarizationMode");
        CSV_FIELD_MAPPING.put("ANTENNA_AZIMUTH", "antennaAzimuth");
        CSV_FIELD_MAPPING.put("AT_RANG", "atRang");
        CSV_FIELD_MAPPING.put("AT_EANG", "atEang");
        CSV_FIELD_MAPPING.put("FEEDER_LOSS", "feedLose");
        CSV_FIELD_MAPPING.put("ANTENNA_GAIN", "antGain");
        CSV_FIELD_MAPPING.put("ALTITUDE", "altitude");
        CSV_FIELD_MAPPING.put("SET_YEAR", "setYear");
        CSV_FIELD_MAPPING.put("SET_MONTH", "setMonth");
        CSV_FIELD_MAPPING.put("EXPAND_STATION", "expandStation");
        CSV_FIELD_MAPPING.put("ATTRIBUTE_STATION", "attributeStation");
        CSV_FIELD_MAPPING.put("ST_SERV_R", "stServR");
        CSV_FIELD_MAPPING.put("ST_SCENE", "stScene");
        CSV_FIELD_MAPPING.put("TRF_DATE", "trfDate");
        CSV_FIELD_MAPPING.put("TRF_USER", "trfUser");
        CSV_FIELD_MAPPING.put("TRF_DATA", "trfData");

        // 初始化必填字段
        REQUIRED_FIELDS.add("CELL_NAME");
        REQUIRED_FIELDS.add("CELL_ID");
        REQUIRED_FIELDS.add("BTS_NAME");
        REQUIRED_FIELDS.add("BTS_ID");
        REQUIRED_FIELDS.add("TECH_TYPE");
        REQUIRED_FIELDS.add("LOCATION");
        REQUIRED_FIELDS.add("LONGITUDE");
        REQUIRED_FIELDS.add("LATITUDE");
        REQUIRED_FIELDS.add("MAX_EMISSIVE_POWER");

        // 初始化数字字段
        NUMERIC_FIELDS.add("LONGITUDE");
        NUMERIC_FIELDS.add("LATITUDE");
        NUMERIC_FIELDS.add("SEND_START_FREQ");
        NUMERIC_FIELDS.add("SEND_END_FREQ");
        NUMERIC_FIELDS.add("ACC_START_FREQ");
        NUMERIC_FIELDS.add("ACC_END_FREQ");
        NUMERIC_FIELDS.add("MAX_EMISSIVE_POWER");
        NUMERIC_FIELDS.add("HEIGHT");
        NUMERIC_FIELDS.add("ANTENNA_AZIMUTH");
        NUMERIC_FIELDS.add("AT_RANG");
        NUMERIC_FIELDS.add("AT_EANG");
        NUMERIC_FIELDS.add("FEEDER_LOSS");
        NUMERIC_FIELDS.add("ANTENNA_GAIN");
        NUMERIC_FIELDS.add("ALTITUDE");
        NUMERIC_FIELDS.add("SET_YEAR");
        NUMERIC_FIELDS.add("SET_MONTH");
        NUMERIC_FIELDS.add("ST_SERV_R");
        NUMERIC_FIELDS.add("TRF_USER");
        NUMERIC_FIELDS.add("TRF_DATA");

        // 初始化字符串长度限制（根据数据库字段长度）
        STRING_LENGTH_LIMITS.put("CELL_NAME", 50);
        STRING_LENGTH_LIMITS.put("CELL_ID", 50);
        STRING_LENGTH_LIMITS.put("BTS_NAME", 80 );
        STRING_LENGTH_LIMITS.put("BTS_ID", 50);
        STRING_LENGTH_LIMITS.put("TECH_TYPE", 60);
        STRING_LENGTH_LIMITS.put("LOCATION", 80);
        STRING_LENGTH_LIMITS.put("COUNTY", 50);
        STRING_LENGTH_LIMITS.put("DEVICE_FACTORY", 50);
        STRING_LENGTH_LIMITS.put("DEVICE_MODEL", 50);
        STRING_LENGTH_LIMITS.put("MODEL_CODE", 50);
        STRING_LENGTH_LIMITS.put("ANTENNA_MODEL", 50);
        STRING_LENGTH_LIMITS.put("ANTENNA_FACTORY", 50);
        STRING_LENGTH_LIMITS.put("POLARIZATION_MODE", 20);
        STRING_LENGTH_LIMITS.put("EXPAND_STATION", 20);
        STRING_LENGTH_LIMITS.put("ATTRIBUTE_STATION", 20);
        STRING_LENGTH_LIMITS.put("ST_SCENE", 50);
    }

    /**
     * 构建文件输入流（支持本地文件和HDFS文件）
     * @param filePath 文件路径
     * @param fileId 文件ID（如果已知）
     * @return 文件输入流
     */
    private InputStream buildFileInputStream(String filePath, Long fileId) throws IOException {
        if (StringUtils.isBlank(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }
        
        System.out.println("buildFileInputStream: 输入路径 = " + filePath + ", 文件ID = " + fileId);
        
        // 如果有文件ID，优先从HDFS获取
        if (fileId != null) {
            try {
                System.out.println("buildFileInputStream: 使用文件ID从HDFS获取文件流，ID = " + fileId);
                InputStream hdfsStream = hdfsWebService.getFileInputStream(fileId.toString());
                if (hdfsStream != null) {
                    System.out.println("buildFileInputStream: 成功从HDFS获取文件流");
                    return hdfsStream;
                } else {
                    System.out.println("buildFileInputStream: 从HDFS获取文件流失败，尝试本地文件");
                }
            } catch (Exception e) {
                System.out.println("buildFileInputStream: 从HDFS获取文件流异常: " + e.getMessage() + "，尝试本地文件");
            }
        }
        
        // 尝试本地文件系统
        String fullPath = buildFullFilePath(filePath);
        File localFile = new File(fullPath);
        if (localFile.exists() && localFile.isFile()) {
            System.out.println("buildFileInputStream: 使用本地文件 = " + fullPath);
            return new FileInputStream(localFile);
        }
        
        throw new IOException("无法找到文件: " + filePath + "（本地路径: " + fullPath + "，文件ID: " + fileId + "）");
    }



    /**
     * 构建完整的本地文件路径
     */
    private String buildFullFilePath(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }
        
        System.out.println("buildFullFilePath: 输入路径 = " + filePath);
        System.out.println("buildFullFilePath: myFilePath = " + myFilePath);
        
        // 如果已经是绝对路径且文件存在，直接返回
        File file = new File(filePath);
        if (file.isAbsolute() && file.exists()) {
            System.out.println("buildFullFilePath: 文件已存在，直接返回 = " + filePath);
            return filePath;
        }
        
        // 处理相对路径
        String relativePath = filePath;
        
        // 去掉开头的斜杠
        if (relativePath.startsWith("/") || relativePath.startsWith("\\")) {
            relativePath = relativePath.substring(1);
        }
        
        // 检查路径是否重复
        // 如果 relativePath 已经包含了 myFilePath 的目录结构，则只取文件名部分
        String basePath = myFilePath;
        
        // 规范化 basePath，确保使用统一的路径分隔符
        basePath = basePath.replace("\\", "/");
        relativePath = relativePath.replace("\\", "/");
        
        // 提取 basePath 中的相对目录部分（去掉驱动器号）
        String baseRelativeDir = basePath;
        if (baseRelativeDir.contains(":")) {
            // Windows 路径，提取盘符后的部分
            int colonIndex = baseRelativeDir.indexOf(":");
            if (colonIndex + 1 < baseRelativeDir.length()) {
                baseRelativeDir = baseRelativeDir.substring(colonIndex + 1);
                if (baseRelativeDir.startsWith("/")) {
                    baseRelativeDir = baseRelativeDir.substring(1);
                }
            }
        }
        
        // 如果 relativePath 包含了 baseRelativeDir，说明路径重复了
        if (relativePath.startsWith(baseRelativeDir)) {
            // 提取文件名部分
            String fileName = relativePath.substring(baseRelativeDir.length());
            if (fileName.startsWith("/")) {
                fileName = fileName.substring(1);
            }
            relativePath = fileName;
        }
        
        // 确保basePath以路径分隔符结尾
        if (!basePath.endsWith("/") && !basePath.endsWith("\\")) {
            basePath += File.separator;
        }
        
        String fullPath = basePath + relativePath;
        
        System.out.println("buildFullFilePath: 最终拼接路径 = " + fullPath);
        
        // 规范化路径
        try {
            String canonicalPath = new File(fullPath).getCanonicalPath();
            System.out.println("buildFullFilePath: 规范化后路径 = " + canonicalPath);
            return canonicalPath;
        } catch (IOException e) {
            // 如果规范化失败，返回拼接后的路径
            String finalPath = fullPath.replace("/", File.separator);
            System.out.println("buildFullFilePath: 规范化失败，返回路径 = " + finalPath);
            return finalPath;
        }
    }

    /**
     * 解析并校验CSV文件
     * @param filePath CSV文件路径
     * @param jobMeta 作业元数据
     * @return 校验结果
     */
    public Map<String, Object> parseAndValidateCsv(String filePath, JobMetaVO jobMeta) {
        return parseAndValidateCsv(filePath, jobMeta, null);
    }

    /**
     * 解析并校验CSV文件（指定编码）
     * @param filePath CSV文件路径
     * @param jobMeta 作业元数据
     * @param encoding 指定的编码，为null则自动检测
     * @return 校验结果
     */
    public Map<String, Object> parseAndValidateCsv(String filePath, JobMetaVO jobMeta, String encoding) {
        return parseAndValidateCsv(filePath, jobMeta, encoding, null);
    }

    /**
     * 解析并校验CSV文件（指定编码和文件ID）
     * @param filePath CSV文件路径
     * @param jobMeta 作业元数据
     * @param encoding 指定的编码，为null则自动检测
     * @param fileId 文件ID（如果已知）
     * @return 校验结果
     */
    public Map<String, Object> parseAndValidateCsv(String filePath, JobMetaVO jobMeta, String encoding, Long fileId) {
        try {
            System.out.println("开始解析CSV文件: " + filePath);
            
            // 检测或指定文件编码
            Charset charset;
            if (StringUtils.isNotBlank(encoding)) {
                try {
                    charset = Charset.forName(encoding);
                    System.out.println("使用指定的文件编码：" + charset.name());
                } catch (Exception e) {
                    System.out.println("指定的编码无效，使用自动检测：" + encoding);
                    charset = detectFileEncoding(filePath, fileId);
                }
            } else {
                System.out.println("开始检测文件编码...");
                charset = detectFileEncoding(filePath, fileId);
                System.out.println("检测到的文件编码：" + charset.name());
            }

            // 读取CSV文件
            System.out.println("开始读取CSV文件...");
            List<Map<String, String>> csvData = readCsvFileWithCharset(filePath, charset, fileId);
            System.out.println("CSV文件读取完成，数据行数: " + csvData.size());
            if (csvData.isEmpty()) {
                // 返回空CsvValidationResultVO，保证类型一致
                CsvValidationResultVO emptyResult = new CsvValidationResultVO(new ArrayList<>(), new ArrayList<>());
                Map<String, Object> result = this.basicReturnFailure("CSV文件为空或格式错误");
                result.put("data", emptyResult);
                return result;
            }

            // 校验表头
            System.out.println("开始校验表头...");
            Map<String, String> headerValidationResult = validateHeaders(csvData.get(0));
            if (headerValidationResult != null) {
                System.out.println("表头校验失败: " + headerValidationResult.get("message"));
                // 返回空CsvValidationResultVO，保证类型一致
                CsvValidationResultVO emptyResult = new CsvValidationResultVO(new ArrayList<>(), new ArrayList<>());
                Map<String, Object> result = this.basicReturnFailure("表头校验失败：" + headerValidationResult.get("message"));
                result.put("data", emptyResult);
                return result;
            }
            System.out.println("表头校验通过");

            // 校验数据行
            System.out.println("开始校验数据行...");
            CsvValidationResultVO validationResult = validateDataRows(csvData, jobMeta);
            System.out.println("数据行校验完成");

            // 文件解析成功就返回success=true，具体的错误数据会在validationResult中
            Map<String, Object> result = this.basicReturnSuccess(validationResult);
            result.put("detectedEncoding", charset.name());
            result.put("success", true);  // 只要文件能正确解析就是success

            return result;

        } catch (Exception e) {
            // 返回空CsvValidationResultVO，保证类型一致
            CsvValidationResultVO emptyResult = new CsvValidationResultVO(new ArrayList<>(), new ArrayList<>());
            Map<String, Object> result = this.basicReturnFailure("CSV解析失败：" + e.getMessage());
            result.put("data", emptyResult);
            return result;
        }
    }

    /**
     * 读取CSV文件为List<Map<String, String>>
     * @param filePath 文件路径
     * @return CSV数据
     */
    private List<Map<String, String>> readCsvFile(String filePath) throws IOException {
        // 自动检测文件编码
        Charset detectedCharset = detectFileEncoding(filePath);
        return readCsvFileWithCharset(filePath, detectedCharset);
    }

    /**
     * 使用指定编码读取CSV文件为List<Map<String, String>>
     * @param filePath 文件路径
     * @param charset 字符编码
     * @return CSV数据
     */
    private List<Map<String, String>> readCsvFileWithCharset(String filePath, Charset charset) throws IOException {
        return readCsvFileWithCharset(filePath, charset, null);
    }

    /**
     * 使用指定编码读取CSV文件为List<Map<String, String>>
     * @param filePath 文件路径
     * @param charset 字符编码
     * @param fileId 文件ID（如果已知）
     * @return CSV数据
     */
    private List<Map<String, String>> readCsvFileWithCharset(String filePath, Charset charset, Long fileId) throws IOException {
        List<Map<String, String>> result = new ArrayList<>();
        
        // 获取文件输入流（支持本地文件和HDFS文件）
        try (InputStream inputStream = buildFileInputStream(filePath, fileId);
             BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, charset))) {

            String line;
            String[] headers = null;
            boolean isFirstLine = true;

            while ((line = reader.readLine()) != null) {
                // 处理BOM
                if (isFirstLine && line.startsWith("\uFEFF")) {
                    line = line.substring(1);
                }

                String[] values = parseCsvLine(line);

                if (isFirstLine) {
                    headers = values;
                    isFirstLine = false;
                } else {
                    Map<String, String> row = new LinkedHashMap<>();
                    for (int i = 0; i < headers.length && i < values.length; i++) {
                        row.put(headers[i], values[i]);
                    }
                    result.add(row);
                }
            }
        }

        return result;
    }

    /**
     * 检测文件编码
     * @param filePath 文件路径
     * @return 检测到的字符编码
     */
    private Charset detectFileEncoding(String filePath) throws IOException {
        return detectFileEncoding(filePath, null);
    }

    /**
     * 检测文件编码
     * @param filePath 文件路径
     * @param fileId 文件ID（如果已知）
     * @return 检测到的字符编码
     */
    private Charset detectFileEncoding(String filePath, Long fileId) throws IOException {
        // 常见的编码列表，按优先级排序
        Charset[] charsets = {
            Charset.forName("GBK"),        // 优先尝试GBK，因为旧系统使用GBK
            StandardCharsets.UTF_8,        // UTF-8
            Charset.forName("GB2312"),     // GB2312
            StandardCharsets.ISO_8859_1    // ISO-8859-1
        };

        // 读取文件前几行进行编码检测
        byte[] buffer = new byte[4096];
        int bytesRead = 0;
        
        try (InputStream inputStream = buildFileInputStream(filePath, fileId)) {
            bytesRead = inputStream.read(buffer);
            if (bytesRead == -1) {
                return StandardCharsets.UTF_8; // 空文件默认UTF-8
            }

            // 尝试每种编码
            for (Charset charset : charsets) {
                try {
                    String content = new String(buffer, 0, bytesRead, charset);

                    // 检查是否包含乱码字符
                    if (isValidEncoding(content, charset)) {
                        return charset;
                    }
                } catch (Exception e) {
                    // 编码转换失败，尝试下一个
                    continue;
                }
            }
        }

        // 如果都失败了，默认使用GBK（因为旧系统使用GBK）
        return Charset.forName("GBK");
    }

    /**
     * 验证编码是否有效
     * @param content 解码后的内容
     * @param charset 使用的字符集
     * @return 是否有效
     */
    private boolean isValidEncoding(String content, Charset charset) {
        // 检查是否包含替换字符（通常表示编码错误）
        if (content.contains("\uFFFD")) {
            return false;
        }

        // 检查是否包含明显的乱码模式（连续的高位字符）
        if (content.matches(".*[\u00C0-\u00FF]{3,}.*")) {
            return false;
        }

        // 检查常见的乱码模式
        if (content.contains("ï¿½") || content.contains("Äú") || content.contains("¸ñ")) {
            return false;
        }

        // 对于GBK编码，检查中文字符是否正常
        if ("GBK".equals(charset.name()) || "GB2312".equals(charset.name())) {
            boolean hasChineseChar = false;
            boolean hasGarbledChar = false;

            for (char c : content.toCharArray()) {
                // 检查中文字符范围
                if (c >= 0x4E00 && c <= 0x9FFF) {
                    hasChineseChar = true;
                }
                // 检查常见乱码字符
                if ((c >= 0x00C0 && c <= 0x00FF) || c == 0xFFFD) {
                    hasGarbledChar = true;
                    break;
                }
            }

            // 如果有中文字符且没有乱码，认为编码正确
            if (hasChineseChar && !hasGarbledChar) {
                return true;
            }

            // 如果有乱码字符，认为编码错误
            if (hasGarbledChar) {
                return false;
            }
        }

        // 对于UTF-8编码，检查是否包含正常的中文字符
        if (StandardCharsets.UTF_8.equals(charset)) {
            for (char c : content.toCharArray()) {
                if (c >= 0x4E00 && c <= 0x9FFF) {
                    // 包含中文字符，认为编码正确
                    return true;
                }
            }
        }

        // 如果没有特殊字符问题，且内容不为空，认为编码有效
        return !content.trim().isEmpty();
    }

    /**
     * 解析CSV行（处理逗号和引号）
     * @param line CSV行
     * @return 字段数组
     */
    private String[] parseCsvLine(String line) {
        List<String> result = new ArrayList<>();
        boolean inQuotes = false;
        StringBuilder currentField = new StringBuilder();

        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);

            if (c == '"') {
                if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
                    // 转义的双引号
                    currentField.append('"');
                    i++; // 跳过下一个引号
                } else {
                    // 切换引号状态
                    inQuotes = !inQuotes;
                }
            } else if (c == ',' && !inQuotes) {
                // 字段分隔符
                result.add(currentField.toString().trim());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }

        // 添加最后一个字段
        result.add(currentField.toString().trim());

        return result.toArray(new String[0]);
    }

    /**
     * 校验表头字段
     * @param firstRow 第一行数据（表头）
     * @return 校验结果，null表示通过
     */
    private Map<String, String> validateHeaders(Map<String, String> firstRow) {
        Set<String> actualHeaders = firstRow.keySet();
        Set<String> expectedHeaders = CSV_FIELD_MAPPING.keySet();

        // 检查字段数量
        if (actualHeaders.size() != expectedHeaders.size()) {
            Map<String, String> error = new HashMap<>();
            error.put("message", String.format("表头字段数量不匹配，期望%d个，实际%d个",
                    expectedHeaders.size(), actualHeaders.size()));
            return error;
        }

        // 检查字段顺序和名称
        List<String> actualHeaderList = new ArrayList<>(actualHeaders);
        List<String> expectedHeaderList = new ArrayList<>(expectedHeaders);

        for (int i = 0; i < expectedHeaderList.size(); i++) {
            if (!expectedHeaderList.get(i).equals(actualHeaderList.get(i))) {
                Map<String, String> error = new HashMap<>();
                error.put("message", String.format("第%d列表头不匹配，期望'%s'，实际'%s'",
                        i + 1, expectedHeaderList.get(i), actualHeaderList.get(i)));
                return error;
            }
        }

        return null; // 校验通过
    }

    /**
     * 校验数据行
     * @param csvData CSV数据
     * @param jobMeta 作业元数据
     * @return 校验结果
     */
    private CsvValidationResultVO validateDataRows(List<Map<String, String>> csvData, JobMetaVO jobMeta) {
        List<Map<String, Object>> validRows = new ArrayList<>();
        List<Map<String, Object>> invalidRows = new ArrayList<>();

        // 获取合法COUNTY集合
//        Set<String> validCounties = regionService.findAllByParentId("32")
//                .stream().map(RegionDTO::getName).collect(Collectors.toSet());
        //获取所有区域
        String orgRegionJson = redisService.get("Caict:regionCounty:orgRegion");
        List<RegionDTO> orgRegionList = JSONObject.parseArray(orgRegionJson, RegionDTO.class);
        Long jobId = jobMeta != null ? jobMeta.getJobId() : null;
        
        // 判断是否需要进行核心表存在性校验（applyType=2：延续  3：注销）
        boolean needCoreTableCheck = jobMeta != null && ("2".equals(jobMeta.getApplyType()) || "3".equals(jobMeta.getApplyType()));

        // 优化1：获取运营商信息和频率规则缓存（避免每行都查询数据库）
        String operatorCode = null;
        Map<String, List<FsaCheckRuleDTO>> frequencyRulesCache = new HashMap<>();
        if (jobMeta != null && jobMeta.getJobId() != null) {
            TransportJobNew job = transportJobNewService.findById(jobMeta.getJobId());
            if (job != null) {
                operatorCode = job.getOperatorCode();
                // 预加载该运营商的所有频率规则并按技术体制缓存
                if (StringUtils.isNotBlank(operatorCode) && !"admin".equals(operatorCode)) {
                    try {
                        List<FsaCheckRuleDTO> allRules = fsaCheckRuleService.findAllByOrgType(operatorCode);
                        for (FsaCheckRuleDTO rule : allRules) {
                            String techType = rule.getNetTs();
                            if (StringUtils.isNotBlank(techType)) {
                                frequencyRulesCache.computeIfAbsent(techType, k -> new ArrayList<>()).add(rule);
                            }
                        }
                        System.out.println("频率规则缓存加载完成，运营商: " + operatorCode + "，技术体制数: " + frequencyRulesCache.size());
                    } catch (Exception e) {
                        System.err.println("加载频率规则缓存失败: " + e.getMessage());
                    }
                }
            }
        }

        // 第一阶段：单个扇区校验，按基站分组
        Map<String, List<Map<String, Object>>> stationGroups = new HashMap<>();
        Map<String, Boolean> stationHasErrors = new HashMap<>();

        System.out.println("=== 数据校验开始 ===");
        System.out.println("CSV原始数据行数: " + csvData.size());

        // 从第一条数据开始处理（注意：csvData已经不包含表头）
        for (int i = 0; i < csvData.size(); i++) {
            Map<String, String> row = csvData.get(i);
            Map<String, Object> processedRow = new LinkedHashMap<>();
            List<String> errors = new ArrayList<>();

            processedRow.put("rowNumber", i + 2); // 行号从2开始（第1行是表头）

            // 校验每个字段
            for (Map.Entry<String, String> entry : CSV_FIELD_MAPPING.entrySet()) {
                String csvField = entry.getKey();
                String entityField = entry.getValue();
                String value = row.get(csvField);

                // 校验必填字段
                if (REQUIRED_FIELDS.contains(csvField) && StringUtils.isBlank(value)) {
                    errors.add(String.format("字段'%s'不能为空", csvField));
                    continue;
                }

                // 校验字符串长度
                if (StringUtils.isNotBlank(value) && STRING_LENGTH_LIMITS.containsKey(csvField)) {
                    int maxLength = STRING_LENGTH_LIMITS.get(csvField);
                    if (value.length() > maxLength) {
                        errors.add(String.format("字段'%s'长度超过限制(%d)，当前长度：%d",
                                csvField, maxLength, value.length()));
                    }
                }

                // 校验数字字段
                if (NUMERIC_FIELDS.contains(csvField) && StringUtils.isNotBlank(value)) {
                    Object convertedValue = validateAndConvertNumericField(csvField, value, errors);
                    processedRow.put(entityField, convertedValue);
                } else {
                    processedRow.put(entityField, value);
                }
            }

            // 获取基站ID用于分组（更改为按基站ID分组，而不是基站名称）
            String stationId = row.get("BTS_ID");
            if (StringUtils.isBlank(stationId)) {
                stationId = "UNKNOWN_STATION_ID_" + i; // 防止基站ID为空的情况
            }

            // 优化2：applyType=2或3的核心表存在性校验（如果不存在则跳出不再进行后续校验）
            if (needCoreTableCheck) {
                boolean sectorExists = validateSectorExistsInCoreTableOptimized(row, errors);
                if (!sectorExists) {
                    // 如果扇区不存在，直接跳出，不再进行后续校验
                    // 添加错误信息到处理后的行
                    processedRow.put("errors", errors);
                    // 标记该基站有错误
                    stationHasErrors.put(stationId, true);
                    // 按基站ID分组
                    stationGroups.computeIfAbsent(stationId, k -> new ArrayList<>()).add(processedRow);
                    continue; // 跳过后续校验，处理下一行数据
                }
            }

            // 优化3：使用缓存的频率规则进行频率范围校验
            validateFrequencyRangeOptimized(row, errors, operatorCode, frequencyRulesCache);

            // 校验COUNTY
            String countyValue = row.get("COUNTY");
            if (countyValue != null && !"".equals(countyValue)) {
                String errorMsg = "字段'COUNTY'不合法，必须为指定地区之一";
                //检验地区，全称转换
                for (RegionDTO regionDTO : orgRegionList) {
                    List<RegionDTO> regionDTOList = regionDTO.getRegionDTOList();
                    if (!regionDTOList.isEmpty()){
                        for (RegionDTO regionDTO1 : regionDTOList) {
                            if (regionDTO1.getName().contains(countyValue)) {
                                errorMsg = regionDTO1.getName();
                                processedRow.put("county", regionDTO1.getName());
                                processedRow.put("regionCode", regionDTO.getCode());
                                break;
                            }
                        }
                    }
                }
                if ("字段'COUNTY'不合法，必须为指定地区之一".equals(errorMsg)){
                    errors.add("字段'COUNTY'不合法，必须为指定地区之一");
                }
            }else {
                errors.add("字段'COUNTY'为空，不符合规定！");
            }

            // 记录该扇区是否有错误
            boolean sectorHasError = !errors.isEmpty();
            
            // 添加错误信息到处理后的行
            if (sectorHasError) {
                processedRow.put("errors", errors);
                // 标记该基站有错误
                stationHasErrors.put(stationId, true);
            }

            // 按基站ID分组
            stationGroups.computeIfAbsent(stationId, k -> new ArrayList<>()).add(processedRow);
        }

        System.out.println("第一阶段校验完成:");
        System.out.println("- 基站总数: " + stationGroups.size());
        System.out.println("- 有错误的基站数: " + stationHasErrors.size());
        
        int totalSectorsBeforeStationLevel = 0;
        int sectorsWithDirectErrors = 0;
        for (List<Map<String, Object>> sectors : stationGroups.values()) {
            totalSectorsBeforeStationLevel += sectors.size();
            for (Map<String, Object> sector : sectors) {
                if (sector.containsKey("errors")) {
                    sectorsWithDirectErrors++;
                }
            }
        }
        System.out.println("- 扇区总数: " + totalSectorsBeforeStationLevel);
        System.out.println("- 直接有错误的扇区数: " + sectorsWithDirectErrors);

        // 第二阶段：基站级校验 - 如果基站下任何扇区有错误，整个基站的所有扇区都标记为错误
        // 同基站下非拉远站的公共参数一致性校验
        for (Map.Entry<String, List<Map<String, Object>>> stationEntry : stationGroups.entrySet()) {
            String stationId = stationEntry.getKey();
            List<Map<String, Object>> stationSectors = stationEntry.getValue();
            boolean stationHasError = stationHasErrors.getOrDefault(stationId, false);

            // 对同基站下非拉远站的公共参数一致性校验
            if (!stationHasError) {
                stationHasError = validateStationCommonParameters(stationSectors, stationId);
                if (stationHasError) {
                    stationHasErrors.put(stationId, true);
                }
            }

            for (Map<String, Object> sector : stationSectors) {
                if (stationHasError) {
                    // 如果基站有错误，所有扇区都进入错误列表
                    if (!sector.containsKey("errors")) {
                        // 原本没有错误的扇区，添加基站级错误
                        List<String> stationLevelErrors = new ArrayList<>();
                        stationLevelErrors.add("同基站下某扇区校验出错，整个基站数据不予通过");
                        sector.put("errors", stationLevelErrors);
                        sector.put("errorType", "STATION_LEVEL_ERROR");
                    } else {
                        // 原本就有错误的扇区，添加错误类型标识
                        sector.put("errorType", "SECTOR_VALIDATION_ERROR");
                    }
                    invalidRows.add(sector);
                } else {
                    // 基站没有错误，扇区进入正确列表
                    validRows.add(sector);
                }
            }
        }

        System.out.println("第二阶段（基站级校验）完成:");
        System.out.println("- 最终正确数据: " + validRows.size());
        System.out.println("- 最终错误数据: " + invalidRows.size());
        System.out.println("- 总计: " + (validRows.size() + invalidRows.size()));
        System.out.println("=== 数据校验结束 ===");

        return new CsvValidationResultVO(validRows, invalidRows);
    }

    /**
     * 校验发射接收频率范围（根据FSA_CHECK_RULE表规则）
     * @param row 数据行
     * @param errors 错误列表
     * @param jobMeta 作业元数据（包含运营商信息）
     */
    private void validateFrequencyRange(Map<String, String> row, List<String> errors, JobMetaVO jobMeta) {
        // 获取必要的频率和技术体制信息
        String techType = row.get("TECH_TYPE");
        String sendStartFreqStr = row.get("SEND_START_FREQ");
        String sendEndFreqStr = row.get("SEND_END_FREQ");
        String accStartFreqStr = row.get("ACC_START_FREQ");
        String accEndFreqStr = row.get("ACC_END_FREQ");
        
        // 检查必要字段是否存在
        if (StringUtils.isBlank(techType) || StringUtils.isBlank(sendStartFreqStr) || 
            StringUtils.isBlank(sendEndFreqStr) || StringUtils.isBlank(accStartFreqStr) || 
            StringUtils.isBlank(accEndFreqStr)) {
            // 如果必要字段有空值，这已经在必填字段校验中处理了，这里不重复添加错误
            return;
        }
        
        // 获取运营商类型
        String operatorCode = null;
        if (jobMeta != null && jobMeta.getJobId() != null) {
            TransportJobNew job = transportJobNewService.findById(jobMeta.getJobId());
            if (job != null) {
                operatorCode = job.getOperatorCode();
            }
        }
        
        if (StringUtils.isBlank(operatorCode)||"admin".equals(operatorCode)) {
            // 如果无法获取运营商信息或者为admin账户，跳过频率校验
            return;
        }
        
        try {
            // 转换频率值为数字类型
            Double sendStartFreq = Double.parseDouble(sendStartFreqStr);
            Double sendEndFreq = Double.parseDouble(sendEndFreqStr);
            Double accStartFreq = Double.parseDouble(accStartFreqStr);
            Double accEndFreq = Double.parseDouble(accEndFreqStr);
            
            // 基本的频率范围逻辑检查
            if (sendStartFreq >= sendEndFreq) {
                errors.add("发射起始频率必须小于发射终止频率");
                return;
            }
            
            if (accStartFreq >= accEndFreq) {
                errors.add("接收起始频率必须小于接收终止频率");
                return;
            }
            
            // 查询该运营商和技术体制下的频率校验规则
            List<FsaCheckRuleDTO> frequencyRules = fsaCheckRuleService.findByOrgGuidAndTechType(operatorCode, techType);
            
            if (frequencyRules == null || frequencyRules.isEmpty()) {
                // 没有找到对应的频率规则，可能是新技术体制或配置问题
                errors.add(String.format("未找到运营商[%s]技术体制[%s]的频率校验规则", getOperatorDisplayName(operatorCode), techType));
                return;
            }
            
            // 检查发射频率和接收频率是否同时满足同一条规则
            boolean isFrequencyValid = false;
            String specificErrorType = null; // 记录具体的错误类型
            
            for (FsaCheckRuleDTO rule : frequencyRules) {
                // 检查发射频率范围和接收频率范围是否同时满足当前规则
                boolean isTransmitFreqValidForThisRule = rule.getFreqEfb() != null && rule.getFreqEfe() != null &&
                    sendStartFreq >= rule.getFreqEfb() && sendEndFreq <= rule.getFreqEfe();
                
                boolean isReceiveFreqValidForThisRule = rule.getFreqRfb() != null && rule.getFreqRfe() != null &&
                    accStartFreq >= rule.getFreqRfb() && accEndFreq <= rule.getFreqRfe();
                
                // 只有当发射频率和接收频率都满足同一条规则时，才认为是合法的
                if (isTransmitFreqValidForThisRule && isReceiveFreqValidForThisRule) {
                    isFrequencyValid = true;
                    break; // 找到匹配的规则后立即结束检查
                }
                
                // 记录具体的错误类型（用于确定错误字段）
                // 优先记录部分匹配的情况（只有一个频率不满足），因为这更能准确定位问题
                if (!isTransmitFreqValidForThisRule && isReceiveFreqValidForThisRule) {
                    specificErrorType = "TRANSMIT_INVALID"; // 只有发射频率不满足（接收频率满足）
                } else if (isTransmitFreqValidForThisRule && !isReceiveFreqValidForThisRule) {
                    specificErrorType = "RECEIVE_INVALID"; // 只有接收频率不满足（发射频率满足）
                } else if (specificErrorType == null && !isTransmitFreqValidForThisRule && !isReceiveFreqValidForThisRule) {
                    specificErrorType = "BOTH_INVALID"; // 发射和接收频率都不满足
                }
            }
            
            // 生成错误信息
            if (!isFrequencyValid) {
                StringBuilder errorMsg = new StringBuilder();
                errorMsg.append(String.format("运营商[%s]技术体制[%s]下", getOperatorDisplayName(operatorCode), techType));
                
                // 根据具体错误类型生成更精确的错误信息
                if ("BOTH_INVALID".equals(specificErrorType)) {
                    errorMsg.append("发射频率和接收频率均未满足任一组合法频率范围");
                } else if ("TRANSMIT_INVALID".equals(specificErrorType)) {
                    errorMsg.append("发射频率未满足合法频率范围");
                } else if ("RECEIVE_INVALID".equals(specificErrorType)) {
                    errorMsg.append("接收频率未满足合法频率范围");
                } else {
                    errorMsg.append("发射频率和接收频率未同时满足任一组合法频率范围");
                }
                
                // 添加合法频率范围提示
                errorMsg.append("，合法频率范围组合：");
                for (int i = 0; i < frequencyRules.size(); i++) {
                    FsaCheckRuleDTO rule = frequencyRules.get(i);
                    if (i > 0) {
                        errorMsg.append("；");
                    }
                    errorMsg.append(String.format("组合%d：发射频率[%.3f-%.3f]MHz，接收频率[%.3f-%.3f]MHz", 
                            i + 1, rule.getFreqEfb(), rule.getFreqEfe(), rule.getFreqRfb(), rule.getFreqRfe()));
                }
                
                errors.add(errorMsg.toString());
            }
            
        } catch (NumberFormatException e) {
            // 频率数字格式错误，这应该在之前的数字字段校验中处理了
            errors.add("频率字段数字格式错误，无法进行频率范围校验");
        } catch (Exception e) {
            // 查询或校验过程中的其他异常
            System.err.println("频率范围校验异常：" + e.getMessage());
            errors.add("频率范围校验过程中发生系统错误");
        }
    }

    /**
     * 优化版：校验发射接收频率范围（使用缓存的频率规则，避免重复数据库查询）
     * @param row 数据行
     * @param errors 错误列表
     * @param operatorCode 运营商代码
     * @param frequencyRulesCache 频率规则缓存（按技术体制分组）
     */
    private void validateFrequencyRangeOptimized(Map<String, String> row, List<String> errors, 
                                               String operatorCode, Map<String, List<FsaCheckRuleDTO>> frequencyRulesCache) {
        // 获取必要的频率和技术体制信息
        String techType = row.get("TECH_TYPE");
        String sendStartFreqStr = row.get("SEND_START_FREQ");
        String sendEndFreqStr = row.get("SEND_END_FREQ");
        String accStartFreqStr = row.get("ACC_START_FREQ");
        String accEndFreqStr = row.get("ACC_END_FREQ");
        
        // 检查必要字段是否存在
        if (StringUtils.isBlank(techType) || StringUtils.isBlank(sendStartFreqStr) || 
            StringUtils.isBlank(sendEndFreqStr) || StringUtils.isBlank(accStartFreqStr) || 
            StringUtils.isBlank(accEndFreqStr)) {
            // 如果必要字段有空值，这已经在必填字段校验中处理了，这里不重复添加错误
            return;
        }
        
        // 检查运营商信息
        if (StringUtils.isBlank(operatorCode) || "admin".equals(operatorCode)) {
            // 如果无法获取运营商信息或者为admin账户，跳过频率校验
            return;
        }
        
        try {
            // 转换频率值为数字类型
            Double sendStartFreq = Double.parseDouble(sendStartFreqStr);
            Double sendEndFreq = Double.parseDouble(sendEndFreqStr);
            Double accStartFreq = Double.parseDouble(accStartFreqStr);
            Double accEndFreq = Double.parseDouble(accEndFreqStr);
            
            // 基本的频率范围逻辑检查
            if (sendStartFreq >= sendEndFreq) {
                errors.add("发射起始频率必须小于发射终止频率");
                return;
            }
            
            if (accStartFreq >= accEndFreq) {
                errors.add("接收起始频率必须小于接收终止频率");
                return;
            }
            
            // 从缓存中获取该技术体制下的频率校验规则
            List<FsaCheckRuleDTO> frequencyRules = frequencyRulesCache.get(techType);
            
            if (frequencyRules == null || frequencyRules.isEmpty()) {
                // 没有找到对应的频率规则，可能是新技术体制或配置问题
                errors.add(String.format("未找到运营商[%s]技术体制[%s]的频率校验规则", getOperatorDisplayName(operatorCode), techType));
                return;
            }
            
            // 检查发射频率和接收频率是否同时满足同一条规则
            boolean isFrequencyValid = false;
            String specificErrorType = null; // 记录具体的错误类型
            
            for (FsaCheckRuleDTO rule : frequencyRules) {
                // 检查发射频率范围和接收频率范围是否同时满足当前规则
                boolean isTransmitFreqValidForThisRule = rule.getFreqEfb() != null && rule.getFreqEfe() != null &&
                    sendStartFreq >= rule.getFreqEfb() && sendEndFreq <= rule.getFreqEfe();
                
                boolean isReceiveFreqValidForThisRule = rule.getFreqRfb() != null && rule.getFreqRfe() != null &&
                    accStartFreq >= rule.getFreqRfb() && accEndFreq <= rule.getFreqRfe();
                
                // 只有当发射频率和接收频率都满足同一条规则时，才认为是合法的
                if (isTransmitFreqValidForThisRule && isReceiveFreqValidForThisRule) {
                    isFrequencyValid = true;
                    break; // 找到匹配的规则后立即结束检查
                }
                
                // 记录具体的错误类型（用于确定错误字段）
                // 优先记录部分匹配的情况（只有一个频率不满足），因为这更能准确定位问题
                if (!isTransmitFreqValidForThisRule && isReceiveFreqValidForThisRule) {
                    specificErrorType = "TRANSMIT_INVALID"; // 只有发射频率不满足（接收频率满足）
                } else if (isTransmitFreqValidForThisRule && !isReceiveFreqValidForThisRule) {
                    specificErrorType = "RECEIVE_INVALID"; // 只有接收频率不满足（发射频率满足）
                } else if (specificErrorType == null && !isTransmitFreqValidForThisRule && !isReceiveFreqValidForThisRule) {
                    specificErrorType = "BOTH_INVALID"; // 发射和接收频率都不满足
                }
            }
            
            // 生成错误信息
            if (!isFrequencyValid) {
                StringBuilder errorMsg = new StringBuilder();
                errorMsg.append(String.format("运营商[%s]技术体制[%s]下", getOperatorDisplayName(operatorCode), techType));
                
                // 根据具体错误类型生成更精确的错误信息
                if ("BOTH_INVALID".equals(specificErrorType)) {
                    errorMsg.append("发射频率和接收频率均未满足任一组合法频率范围");
                } else if ("TRANSMIT_INVALID".equals(specificErrorType)) {
                    errorMsg.append("发射频率未满足合法频率范围");
                } else if ("RECEIVE_INVALID".equals(specificErrorType)) {
                    errorMsg.append("接收频率未满足合法频率范围");
                } else {
                    errorMsg.append("发射频率和接收频率未同时满足任一组合法频率范围");
                }
                
                // 添加合法频率范围提示
                errorMsg.append("，合法频率范围组合：");
                for (int i = 0; i < frequencyRules.size(); i++) {
                    FsaCheckRuleDTO rule = frequencyRules.get(i);
                    if (i > 0) {
                        errorMsg.append("；");
                    }
                    errorMsg.append(String.format("组合%d：发射频率[%.3f-%.3f]MHz，接收频率[%.3f-%.3f]MHz", 
                            i + 1, rule.getFreqEfb(), rule.getFreqEfe(), rule.getFreqRfb(), rule.getFreqRfe()));
                }
                
                errors.add(errorMsg.toString());
            }
            
        } catch (NumberFormatException e) {
            // 频率数字格式错误，这应该在之前的数字字段校验中处理了
            errors.add("频率字段数字格式错误，无法进行频率范围校验");
        } catch (Exception e) {
            // 校验过程中的其他异常
            System.err.println("频率范围校验异常：" + e.getMessage());
            errors.add("频率范围校验过程中发生系统错误");
        }
    }
    
    /**
     * 获取运营商显示名称
     * @param operatorCode 运营商代码
     * @return 运营商显示名称
     */
    private String getOperatorDisplayName(String operatorCode) {
        if (operatorCode == null) {
            return "未知运营商";
        }
        switch (operatorCode.toLowerCase()) {
            case "telecom":
                return "中国电信";
            case "mobile":
                return "中国移动";
            case "unicom":
                return "中国联通";
            case "guangdian":
                return "中国广电";
            default:
                return operatorCode;
        }
    }

    /**
     * 校验扇区在核心表中是否存在（仅用于 applyType=2：延续 和 applyType=3：注销）
     * @param row 数据行
     * @param errors 错误列表
     */
    private void validateSectorExistsInCoreTable(Map<String, String> row, List<String> errors) {
        // 构建业务逻辑唯一键
        String cellName = row.get("CELL_NAME");
        String cellId = row.get("CELL_ID");
        String btsName = row.get("BTS_NAME");
        String btsId = row.get("BTS_ID");
        
        // 检查必要字段是否存在
        if (StringUtils.isBlank(cellName) || StringUtils.isBlank(cellId) || 
            StringUtils.isBlank(btsName) || StringUtils.isBlank(btsId)) {
            // 如果业务逻辑唯一键字段有空值，这已经在必填字段校验中处理了，这里不重复添加错误
            return;
        }
        
        try {
            // 调用 Service 查询核心表 APPROVAL_RAW_BTS
            boolean exists = asyncRawBtsService.existsByBusinessKey(cellName, cellId, btsName, btsId);
            
            if (!exists) {
                errors.add("该扇区不存在，无法执行延续/注销操作");
            }
        } catch (Exception e) {
            // 查询异常时记录日志并添加错误信息
            System.err.println("查询核心表失败：" + e.getMessage());
            errors.add("系统错误：无法验证扇区是否存在");
        }
    }

    /**
     * 优化版：校验扇区在核心表中是否存在（返回是否存在的布尔值）
     * @param row 数据行
     * @param errors 错误列表
     * @return 扇区是否存在
     */
    private boolean validateSectorExistsInCoreTableOptimized(Map<String, String> row, List<String> errors) {
        // 构建业务逻辑唯一键
        String cellName = row.get("CELL_NAME");
        String cellId = row.get("CELL_ID");
        String btsName = row.get("BTS_NAME");
        String btsId = row.get("BTS_ID");
        
        // 检查必要字段是否存在
        if (StringUtils.isBlank(cellName) || StringUtils.isBlank(cellId) || 
            StringUtils.isBlank(btsName) || StringUtils.isBlank(btsId)) {
            // 如果业务逻辑唯一键字段有空值，这已经在必填字段校验中处理了，这里不重复添加错误
            return true; // 如果必填字段为空，让必填字段校验处理，这里不阻断
        }
        
        try {
            // 调用 Service 查询核心表 APPROVAL_RAW_BTS
            boolean exists = asyncRawBtsService.existsByBusinessKey(cellName, cellId, btsName, btsId);
            
            if (!exists) {
                errors.add("该扇区不存在，无法执行延续/注销操作");
                return false; // 扇区不存在，返回false以便调用方跳出后续校验
            }
            
            return true; // 扇区存在，继续后续校验
        } catch (Exception e) {
            // 查询异常时记录日志并添加错误信息
            System.err.println("查询核心表失败：" + e.getMessage());
            errors.add("系统错误：无法验证扇区是否存在");
            return false; // 查询异常时也跳出后续校验
        }
    }

    /**
     * 校验同基站下非拉远站的公共参数一致性
     * @param stationSectors 基站下的所有扇区数据
     * @param stationId 基站ID
     * @return 是否存在错误
     */
    private boolean validateStationCommonParameters(List<Map<String, Object>> stationSectors, String stationId) {
        if (stationSectors == null || stationSectors.size() <= 1) {
            return false; // 单个扇区或空列表不需要校验
        }

        // 过滤出非拉远站的扇区（expandStation=1表示非拉远站）
        List<Map<String, Object>> nonRemoteSectors = new ArrayList<>();
        for (Map<String, Object> sector : stationSectors) {
            String expandStation = getStringValue(sector, "expandStation");
            if ("1".equals(expandStation)) {
                nonRemoteSectors.add(sector);
            }
        }

        // 如果非拉远站扇区数量少于2个，不需要校验
        if (nonRemoteSectors.size() <= 1) {
            return false;
        }

        // 以第一个非拉远站扇区作为基准
        Map<String, Object> referenceSector = nonRemoteSectors.get(0);
        
        // 定义需要校验的公共参数字段
        String[] commonFields = {
            "btsName",      // 基站名称
            "btsId",        // 基站识别码
            "techType",     // 技术类型
            "location",     // 位置
            "county",       // 区县
            "longitude",    // 经度
            "latitude",     // 纬度
            "altitude"      // 海拔高度
        };

        // 校验每个非拉远站扇区的公共参数是否与基准一致
        for (int i = 1; i < nonRemoteSectors.size(); i++) {
            Map<String, Object> currentSector = nonRemoteSectors.get(i);
            
            for (String field : commonFields) {
                String referenceValue = getStringValue(referenceSector, field);
                String currentValue = getStringValue(currentSector, field);
                
                // 处理null值比较
                if (!Objects.equals(referenceValue, currentValue)) {
                    // 发现不一致的公共参数，为该扇区添加错误信息
                    @SuppressWarnings("unchecked")
                    List<String> errors = (List<String>) currentSector.get("errors");
                    if (errors == null) {
                        errors = new ArrayList<>();
                        currentSector.put("errors", errors);
                    }
                    
                    // 添加具体的错误信息
                    String fieldDisplayName = getFieldDisplayName(field);
                    errors.add(String.format("同基站下非拉远站的%s必须一致，期望值：%s，实际值：%s", 
                            fieldDisplayName, 
                            referenceValue != null ? referenceValue : "空", 
                            currentValue != null ? currentValue : "空"));
                    
                    currentSector.put("errorType", "STATION_COMMON_PARAMETER_ERROR");
                    
                    System.out.println(String.format("基站[%s]公共参数不一致：字段[%s]，期望值[%s]，实际值[%s]", 
                            stationId, fieldDisplayName, referenceValue, currentValue));
                }
            }
        }

        // 检查是否有扇区被添加了错误信息
        for (Map<String, Object> sector : nonRemoteSectors) {
            if (sector.containsKey("errors")) {
                @SuppressWarnings("unchecked")
                List<String> errors = (List<String>) sector.get("errors");
                if (errors != null && !errors.isEmpty()) {
                    // 检查是否包含公共参数错误
                    for (String error : errors) {
                        if (error.contains("同基站下非拉远站的") && error.contains("必须一致")) {
                            return true; // 发现公共参数不一致错误
                        }
                    }
                }
            }
        }

        return false;
    }

    /**
     * 获取字段的显示名称
     * @param fieldName 字段名
     * @return 显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        switch (fieldName) {
            case "btsName":
                return "基站名称";
            case "btsId":
                return "基站识别码";
            case "techType":
                return "技术类型";
            case "location":
                return "位置";
            case "county":
                return "区县";
            case "longitude":
                return "经度";
            case "latitude":
                return "纬度";
            case "altitude":
                return "海拔高度";
            default:
                return fieldName;
        }
    }

    /**
     * 校验并转换数字字段
     * @param fieldName 字段名
     * @param value 字段值
     * @param errors 错误列表
     * @return 转换后的值
     */
    private Object validateAndConvertNumericField(String fieldName, String value, List<String> errors) {
        try {
            // 检查是否包含字母
            Pattern letterPattern = Pattern.compile("[a-zA-Z]");
            if (letterPattern.matcher(value).find()) {
                errors.add(String.format("字段'%s'不能包含字母，当前值：%s", fieldName, value));
                return value;
            }

            // 根据字段类型转换
            if ("SET_YEAR".equals(fieldName) || "SET_MONTH".equals(fieldName)) {
                int intValue = Integer.parseInt(value);

                // 校验年份范围
                if ("SET_YEAR".equals(fieldName) && (intValue < 1900 || intValue > 2100)) {
                    errors.add(String.format("字段'%s'年份范围应在1900-2100之间，当前值：%d", fieldName, intValue));
                }

                // 校验月份范围
                if ("SET_MONTH".equals(fieldName) && (intValue < 1 || intValue > 12)) {
                    errors.add(String.format("字段'%s'月份范围应在1-12之间，当前值：%d", fieldName, intValue));
                }

                return intValue;
            } else {
                BigDecimal decimalValue = new BigDecimal(value);

                // 校验功率字段范围
                if ("MAX_EMISSIVE_POWER".equals(fieldName)) {
                    if (decimalValue.compareTo(BigDecimal.ZERO) < 0 ||
                        decimalValue.compareTo(new BigDecimal("500")) > 0) {
                        errors.add(String.format("字段'%s'功率范围应在0-500之间，当前值：%s", fieldName, value));
                    }
                }

                return decimalValue;
            }
        } catch (NumberFormatException e) {
            errors.add(String.format("字段'%s'数字格式错误，当前值：%s", fieldName, value));
            return value;
        }
    }


    /**
     * 提交任务（重构：任务只能提交一次，以提交为边界处理所有数据）
     * 
     * 业务规则：
     * 1. 新任务：使用uploadToken关联文件，提交时创建jobId
     * 2. 编辑任务：使用jobId关联文件，仅限未提交的任务
     * 3. 提交后任务锁定，不可追加、修改或覆盖
     * 4. 每次提交都是完整的数据快照
     * 5. 数据处理改为异步，避免前端长时间等待
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> submitJob(SubmitJobVO submitJobVO, String token) {
        try {
/*            // 通过token获取用户信息
            String createdBy = "admin"; // 设置创建人姓名
            Long userId = Long.valueOf(1234565454); // 设置用户ID
            String operatorCode = "CBN";*/

            // 通过token获取用户信息
            UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
            if (usersDTO == null) {
                return this.basicReturnFailure(401);
            }

            String createdBy = usersDTO.getName(); // 设置上传人姓名
            String operatorCode=usersDTO.getType();
            Long userId = Long.valueOf(usersDTO.getId()); // 设置用户ID

            // 从VO对象中获取参数
            String uploadToken = submitJobVO.getUploadToken();
            String applyType = submitJobVO.getApplyType();
            Long jobId = submitJobVO.getJobId();
            String jobName = submitJobVO.getJobName();

            TransportJobNew job;
            List<TransportFile> files;

            // 判断是新任务还是编辑任务
            if (jobId == null) {
                // 新任务流程：使用uploadToken
                if (StringUtils.isBlank(uploadToken)) {
                    return this.basicReturnFailure("新任务必须提供uploadToken");
                }

                // 检查uploadToken关联的文件
                files = transportFileService.findByUploadToken(uploadToken);
                if (files.isEmpty()) {
                    return this.basicReturnFailure("未找到与uploadToken关联的文件");
                }

                // 创建新任务
                job = new TransportJobNew();
                job.setOperatorCode(operatorCode);
                job.setStatus("checking"); // 设置为校验中状态，异步处理完成后会更新
                job.setApplyType(applyType);
                job.setCreatedBy(createdBy);
                job.setUserId(userId);
                job.setJobName(jobName);
                job.setCreatedAt(new Date());
                job.setUpdatedAt(new Date());

                // 保存任务并获取ID
                Object savedId = transportJobNewService.save(job);
                jobId = extractJobId(savedId, job);
                if (jobId == null) {
                    return this.basicReturnFailure("任务创建失败，无法获取任务ID");
                }

                // 将uploadToken关联的文件绑定到新任务
                transportFileService.updateJobIdAndClearToken(jobId, uploadToken);

            } else {
                // 编辑任务流程：使用jobId
                job = transportJobNewService.findById(jobId);
                if (job == null) {
                    return this.basicReturnFailure("任务不存在");
                }

                // 检查任务是否已经提交过
                if (isTaskSubmitted(job.getStatus())) {
                    return this.basicReturnFailure("任务已提交，不可重复提交或修改。当前状态：" + job.getStatus());
                }

                // 获取任务关联的文件
                files = transportFileService.findByJobId(jobId);
                if (files.isEmpty()) {
                    return this.basicReturnFailure("任务下未找到关联文件");
                }

                // 更新任务状态为校验中
                job.setStatus("checking");
                job.setUpdatedAt(new Date());
                if (jobName != null) {
                    job.setJobName(jobName);
                }
                transportJobNewService.update(job);
            }

            // 异步处理数据，避免前端长时间等待
            final Long finalJobId = jobId;
            CompletableFuture.runAsync(() -> {
                try {
                    // 添加短暂延迟，确保前端能看到processing状态
                    Thread.sleep(1000); // 1秒延迟
                    
                    System.out.println("开始异步处理任务: " + finalJobId);
                    
                    // 重新查询最新的文件状态，避免使用过期的文件对象
                    List<TransportFile> latestFiles = transportFileService.findByJobId(finalJobId);
                    System.out.println("异步处理 - 查询到文件数量: " + latestFiles.size());
                    
                    Map<String, Object> processingResult = processSubmissionData(finalJobId, latestFiles, usersDTO);
                    
                    // 重新获取任务对象（可能在异步处理过程中有其他更新）
                    TransportJobNew latestJob = transportJobNewService.findById(finalJobId);
                    if (latestJob != null) {
                        // 更新最终任务状态
                        updateFinalTaskStatus(latestJob, processingResult);
                    }
                    
                    System.out.println("任务 " + finalJobId + " 异步数据处理完成");
                    
                } catch (Exception e) {
                    System.err.println("任务 " + finalJobId + " 异步数据处理失败: " + e.getMessage());
                    e.printStackTrace();
                    
                    // 异步处理失败时，更新任务状态为处理失败
                    try {
                        TransportJobNew failedJob = transportJobNewService.findById(finalJobId);
                        if (failedJob != null) {
                            failedJob.setStatus("invalid_data");
                            failedJob.setUpdatedAt(new Date());
                            transportJobNewService.update(failedJob);
                        }
                    } catch (Exception updateEx) {
                        System.err.println("更新失败任务状态异常: " + updateEx.getMessage());
                    }
                }
            }, asyncExecutor);

            // 立即返回任务提交成功的响应，不等待数据处理完成
            Map<String, Object> result = new HashMap<>();
            result.put("jobId", String.valueOf(jobId));
            result.put("submissionTime", new Date());
            result.put("status", "checking");
            result.put("message", "任务提交成功，正在后台处理数据，请稍后查看处理结果");
            
            return this.basicReturnSuccess(result);

        } catch (Exception e) {
            return this.basicReturnFailure("提交任务失败：" + e.getMessage());
        }
    }

    /**
     * 检查任务是否已经提交过
     * @param status 任务状态
     * @return 是否已提交
     */
    private boolean isTaskSubmitted(String status) {
        // 已提交的状态列表（添加checking状态，表示正在校验中也属于已提交状态）
        List<String> submittedStatuses = Arrays.asList(
            "checking",     // 校验中（异步处理）
            "checked",      // 已校验
            "invalid_data", // 数据无效
            "approving",    // 审批中
            "approved",     // 审批通过
            "rejected",     // 审批驳回
            "done",         // 完成
            "revoked"       // 已撤销
        );
        return submittedStatuses.contains(status);
    }

    /**
     * 提取任务ID
     * @param savedId 保存返回的ID
     * @param job 任务对象
     * @return 任务ID
     */
    private Long extractJobId(Object savedId, TransportJobNew job) {
        if (savedId instanceof String) {
            return Long.valueOf((String) savedId);
        } else if (savedId instanceof Long) {
            return (Long) savedId;
        } else {
            // 如果save方法直接设置了ID到实体对象中
            return job.getId();
        }
    }

    /**
     * 处理提交的数据（以本次提交为边界）
     * @param jobId 任务ID
     * @param files 文件列表
     * @return 处理结果
     */
    private Map<String, Object> processSubmissionData(Long jobId, List<TransportFile> files,UsersDTO usersDTO) {
        Map<String, Object> result = new HashMap<>();
        boolean hasProcessingError = false;
        int totalProcessedFiles = 0;
        int successfulFiles = 0;
        int failedFiles = 0;
        int totalCsvFiles = 0;  // 新增：CSV文件总数统计
        int skippedFiles = 0;   // 新增：跳过的文件数统计

        System.out.println("processSubmissionData: 开始处理任务 " + jobId + "，文件数量: " + files.size());
        
        // 获取任务信息，用于传递applyType给校验方法
        TransportJobNew jobInfo = transportJobNewService.findById(jobId);
        if (jobInfo == null) {
            result.put("totalProcessedFiles", 0);
            result.put("successfulFiles", 0);
            result.put("failedFiles", 0);
            result.put("hasProcessingError", true);
            return result;
        }
        
        // 先统计CSV文件数量，确保有CSV文件需要处理
        for (TransportFile file : files) {
            if ("1".equals(file.getFileType()) && file.getStatus() != null && file.getStatus() == 1) {
                totalCsvFiles++;
            }
        }
        
        System.out.println("processSubmissionData: 需要处理的CSV文件数量: " + totalCsvFiles);
        
        // 如果没有CSV文件需要处理，直接返回错误
        if (totalCsvFiles == 0) {
            System.out.println("processSubmissionData: 没有找到需要处理的CSV文件");
            result.put("totalProcessedFiles", 0);
            result.put("successfulFiles", 0);
            result.put("failedFiles", 0);
            result.put("hasProcessingError", true);
            result.put("errorMessage", "没有找到需要处理的CSV数据文件");
            return result;
        }
        
        // 处理CSV(fileType="1")且未校验(status=1)的文件
        for (TransportFile file : files) {
            System.out.println("检查文件: ID=" + file.getId() + ", fileType=" + file.getFileType() + ", status=" + file.getStatus());
            if (!("1".equals(file.getFileType()) && file.getStatus() != null && file.getStatus() == 1)) {
                System.out.println("跳过文件: ID=" + file.getId() + ", 原因: fileType=" + file.getFileType() + " != '1' 或 status=" + file.getStatus() + " != 1");
                skippedFiles++;
                continue; // 跳过非CSV或已校验的文件
            }
            System.out.println("开始处理文件: ID=" + file.getId() + ", filePath=" + file.getFilePath());

            totalProcessedFiles++;
            
            try {
                JobMetaVO jobMeta = new JobMetaVO();
                jobMeta.setJobId(jobId);
                jobMeta.setApplyType(jobInfo.getApplyType()); // 设置申请类型，用于核心表存在性校验
                
                System.out.println("准备解析CSV文件: " + file.getFilePath());
                // 解析和校验CSV文件，传递文件ID以支持HDFS
                Map<String, Object> checkResult = this.parseAndValidateCsv(file.getFilePath(), jobMeta, "GBK", file.getId());
                System.out.println("CSV解析完成，结果: " + checkResult);
                System.out.println("success字段值: " + checkResult.get("success"));
                System.out.println("message字段值: " + checkResult.get("message"));
                CsvValidationResultVO validationResult = (CsvValidationResultVO) checkResult.get("data");
                
                if (Boolean.TRUE.equals(checkResult.get("success"))) {
                    // 文件解析成功，处理数据（包含与历史错误数据的比对）
                    this.processSubmissionDataWithHistoryComparison(
                            validationResult.getValidRows(),
                            validationResult.getInvalidRows(),
                            jobId,usersDTO
                    );
                    
                    // 更新文件状态为已处理
                    transportFileService.updateFileStatus(file.getId(), 2);
                    successfulFiles++;
                } else {
                    // 文件解析失败
                    transportFileService.updateFileStatus(file.getId(), 3);
                    failedFiles++;
                    hasProcessingError = true;
                }
            } catch (Exception e) {
                // 处理异常
                System.out.println("CSV处理异常: " + e.getClass().getSimpleName() + " - " + e.getMessage());
                e.printStackTrace();
                transportFileService.updateFileStatus(file.getId(), 3);
                failedFiles++;
                hasProcessingError = true;
            }
        }

        result.put("totalProcessedFiles", totalProcessedFiles);
        result.put("successfulFiles", successfulFiles);
        result.put("failedFiles", failedFiles);
        result.put("hasProcessingError", hasProcessingError);
        result.put("totalCsvFiles", totalCsvFiles);
        result.put("skippedFiles", skippedFiles);
        result.put("totalFiles", files.size());
        
        System.out.println("processSubmissionData: 处理完成 - 总文件数:" + files.size() + 
                          ", CSV文件数:" + totalCsvFiles + 
                          ", 处理文件数:" + totalProcessedFiles + 
                          ", 成功:" + successfulFiles + 
                          ", 失败:" + failedFiles + 
                          ", 跳过:" + skippedFiles);
        
        return result;
    }

    /**
     * 处理提交数据并与历史错误数据进行比对（优化版本：批量处理）
     * @param validRows 正确数据行
     * @param invalidRows 错误数据行
     * @param jobId 任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void processSubmissionDataWithHistoryComparison(List<Map<String, Object>> validRows, 
                                                          List<Map<String, Object>> invalidRows, 
                                                          Long jobId,UsersDTO usersDTO) {
        // 处理正确数据（每批任务的正确数据都入库，不跳过重复数据）
        List<TransportSchedule> transportSchedules = new ArrayList<>();
        String orgRegionJson = redisService.get("Caict:regionCounty:orgRegion");
        TransportJobNew transportJobNew = transportJobNewService.findById(jobId);
        //获取规则
        List<FsaCheckRuleDTO> fsaCheckRuleDTOList = fsaCheckRuleService.findByOrgType(transportJobNew.getOperatorCode());
        // 将列表转换为 Map，键为 ID，值为姓名
        Map<String, String> tsMap = fsaCheckRuleDTOList.stream()
                .collect(Collectors.toMap(
                        FsaCheckRuleDTO::getNetTs,    // 键：用户 ID
                        FsaCheckRuleDTO::getGenNum   // 值：用户姓名
                        // 处理键冲突的情况（这里假设 ID 唯一，不会冲突）
                ));
        //获取所有区域
        List<RegionDTO> orgRegionList = JSONObject.parseArray(orgRegionJson, RegionDTO.class);

        // 优化：批量处理数据集合
        List<TransportRawBtsDealNew> dealNewList = new ArrayList<>();
        
        // 优化1：预处理地区分支映射，避免重复查询（改为使用regionCode）
        Map<String, Long> regionToBranchIdMap = new HashMap<>();
        Map<Long, Integer> branchCountMap = new HashMap<>(); // 统计每个分支的数据量
        Set<String> uniqueRegionCodes = validRows.stream()
                .map(row -> getStringValue(row, "regionCode"))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        // 批量查询或创建地区分支（使用regionCode）
        for (String regionCode : uniqueRegionCodes) {
            // 获取对应的regionName用于显示
            String regionName = getRegionNameByCode(regionCode, orgRegionList);
            TransportJobBranchNew branch = transportJobBranchNewService.findOrCreateByJobIdAndRegionCode(jobId, regionCode, regionName);
            regionToBranchIdMap.put(regionCode, branch.getId());
            branchCountMap.put(branch.getId(), 0); // 初始化计数
        }
        
        // 优化2：批量查询历史错误记录，避免逐条查询
        List<TransportRawBtsDealNew> tempEntitiesForHistoryCheck = validRows.stream()
                .map(row -> convertToTransportRawBtsDealNew(row, jobId))
                .collect(Collectors.toList());
        
        Map<String, TransportRawBtsDealLogNew> historicalErrorMap = batchQueryHistoricalErrors(tempEntitiesForHistoryCheck);
        
        // 处理正确数据
        for (Map<String, Object> row : validRows) {
            String regionCode = getStringValue(row, "regionCode");
            
            // 使用预处理的地区分支映射（使用regionCode）
            Long jobBranchId = regionToBranchIdMap.get(regionCode);
            if (jobBranchId == null) {
                // 异常情况处理：如果预处理中没有找到，重新查询
                String county = getStringValue(row, "county");
                String regionName = getRegionNameByCode(regionCode, orgRegionList);
                TransportJobBranchNew branch = transportJobBranchNewService.findOrCreateByJobIdAndRegionCode(jobId, regionCode, regionName != null ? regionName : county);
                jobBranchId = branch.getId();
                regionToBranchIdMap.put(regionCode, jobBranchId);
                branchCountMap.put(jobBranchId, 0);
            }
            
            // 创建临时实体对象用于业务逻辑唯一键比较
            TransportRawBtsDealNew tempEntity = convertToTransportRawBtsDealNew(row, jobId);
            
            // 使用批量查询的结果检查历史错误记录
            String businessKey = buildBusinessLogicKey(tempEntity);
            TransportRawBtsDealLogNew historicalError = historicalErrorMap.get(businessKey);
            
            // 注意：历史错误记录的删除仍然需要逐条处理，因为需要确保数据一致性
            if (historicalError != null) {
                // 删除历史错误记录（数据修复），即使技术参数发生了变化也能正确匹配
                transportRawBtsDealLogNewService.deleteById(historicalError.getId());
            }
            
            // 设置jobBranchId并收集正确数据
            row.put("jobBranchId", jobBranchId);
            dealNewList.add(convertToTransportRawBtsDealNew(row, jobId));
            transportSchedules.add(convertToTransportSchedule(row,transportJobNew,orgRegionList,tsMap));
            
            // 优化：在内存中累加计数，而不是每次都更新数据库
            branchCountMap.put(jobBranchId, branchCountMap.get(jobBranchId) + 1);
        }

        // 优化3：批量更新地区分支计数
        if (!branchCountMap.isEmpty()) {
            transportJobBranchNewService.batchAdjustTotalCount(branchCountMap);
        }

        //这里先判断APPROVAL_TRANSPORT_JOB各地区数据是否审核完成
        List<String> approvalTransportJobList = approvalTransportJobWebService.findRegionCodeByStatus(Arrays.asList("revoked", "done"),usersDTO,null);
        if (approvalTransportJobList.isEmpty()){
            if ("1".equals(transportJobNew.getApplyType())){
                //开启线程进行新增/变更比对处理
                CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation1(String.valueOf(jobId),null,transportSchedules,usersDTO), asyncExecutor);
//                ThreadPoolUtil.getThread().execute(() -> transportJobWebService.checkExpandStation1(String.valueOf(jobId),null,transportSchedules,usersDTO));
            }else if ("2".equals(transportJobNew.getApplyType()) || "3".equals(transportJobNew.getApplyType())){
                //开启线程进行注销处理
//                ThreadPoolUtil.getThread().execute(() -> transportJobWebService.checkExpandStation2(String.valueOf(jobId),null,transportSchedules,usersDTO));
                CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation2(String.valueOf(jobId),null,transportSchedules,usersDTO), asyncExecutor);
            }
        }else {
            //判断前置任务是否完成
            Map<String, List<TransportSchedule>> collect = transportSchedules.stream().collect(Collectors.groupingBy(TransportSchedule::getRegionCode));
            if (!collect.isEmpty()){
                for (Map.Entry<String, List<TransportSchedule>> entry : collect.entrySet()) {
                    if (!approvalTransportJobList.contains(entry.getKey())){
                        //开启线程进行新增/变更比对处理
                        if ("1".equals(transportJobNew.getApplyType())){
                            //开启线程进行新增/变更比对处理
                            CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation1(String.valueOf(jobId),null,transportSchedules,usersDTO), asyncExecutor);
//                            ThreadPoolUtil.getThread().execute(() -> transportJobWebService.checkExpandStation1(String.valueOf(jobId),null,entry.getValue(),usersDTO));
                        }else if ("2".equals(transportJobNew.getApplyType()) || "3".equals(transportJobNew.getApplyType())){
                            //开启线程进行延续/注销处理
//                            ThreadPoolUtil.getThread().execute(() -> transportJobWebService.checkExpandStation2(String.valueOf(jobId),null,entry.getValue(),usersDTO));
                            CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation2(String.valueOf(jobId),null,transportSchedules,usersDTO), asyncExecutor);
                        }
                    }else {
                        //数据插入待办表等待对比
                        transportScheduleService.insertBatchCreateId(entry.getValue());
                    }
                }
            }
        }


        // 处理错误数据
        if (!invalidRows.isEmpty()) {
            System.out.println("=== 开始处理错误数据 ===");
            System.out.println("错误数据总数: " + invalidRows.size());
            
            // 批量查询现有错误记录，避免重复插入
            List<TransportRawBtsDealNew> tempEntitiesForQuery = invalidRows.stream()
                    .map(row -> convertToTransportRawBtsDealNew(row, jobId))
                    .collect(Collectors.toList());
            
            Map<String, TransportRawBtsDealLogNew> existingErrorMap = batchQueryExistingErrors(jobId, tempEntitiesForQuery);
            System.out.println("查询到已存在的错误记录数: " + existingErrorMap.size());

            List<TransportRawBtsDealLogNew> newLogEntityList = new ArrayList<>();
            List<TransportRawBtsDealLogNew> updateLogEntityList = new ArrayList<>();

            for (Map<String, Object> row : invalidRows) {
                @SuppressWarnings("unchecked")
                List<String> errorMessages = (List<String>) row.get("errors");
                String primaryErrorMessage = (errorMessages != null && !errorMessages.isEmpty()) 
                        ? errorMessages.get(0) : "未知错误";

                // 创建业务逻辑唯一键
                TransportRawBtsDealNew tempEntity = convertToTransportRawBtsDealNew(row, jobId);
                String businessKey = buildBusinessLogicKey(tempEntity);

                if (existingErrorMap.containsKey(businessKey)) {
                    // 已存在该错误记录，准备更新
                    TransportRawBtsDealLogNew existingLog = existingErrorMap.get(businessKey);
                    existingLog.setErrorMessage(sqlSafeString(primaryErrorMessage));
                    existingLog.setDealData(JSON.toJSONString(cleanRowDataForLog(row)));
                    existingLog.setCreatedAt(new Date());  // 更新时间字段
                    updateLogEntityList.add(existingLog);
                } else {
                    // 新的错误记录，准备插入
                    TransportRawBtsDealLogNew logEntity = convertToTransportRawBtsDealLogNew(row, jobId, primaryErrorMessage);
                    newLogEntityList.add(logEntity);
                }
            }

            System.out.println("准备新增错误记录数: " + newLogEntityList.size());
            System.out.println("准备更新错误记录数: " + updateLogEntityList.size());

            // 批量处理错误记录
            if (!newLogEntityList.isEmpty()) {
                try {
                    transportRawBtsDealLogNewService.insertBatchCreateId(newLogEntityList);
                    System.out.println("成功新增错误记录: " + newLogEntityList.size() + " 条");
                } catch (Exception e) {
                    System.err.println("新增错误记录失败: " + e.getMessage());
                    throw e;
                }
            }

            if (!updateLogEntityList.isEmpty()) {
                try {
                    transportRawBtsDealLogNewService.updateBatch(updateLogEntityList);
                    System.out.println("成功更新错误记录: " + updateLogEntityList.size() + " 条");
                } catch (Exception e) {
                    System.err.println("更新错误记录失败: " + e.getMessage());
                    throw e;
                }
            }
            
            System.out.println("=== 错误数据处理完成 ===");
        }

        // 批量执行数据库操作
        if (!dealNewList.isEmpty()){
            transportRawBtsDealNewService.insertBatchCreateId(dealNewList);
        }
    }
    
    /**
     * 清理行数据中可能导致 SQL 和 JSON 问题的字段
     * @param row 原始行数据
     * @return 清理后的行数据
     */
    private Map<String, Object> cleanRowDataForLog(Map<String, Object> row) {
        Map<String, Object> cleanRow = new HashMap<>(row);
        cleanRow.remove("errors");     // 移除 errors 字段（包含单引号的错误信息）
        cleanRow.remove("errorType");  // 移除 errorType 字段（可能包含特殊字符）
        cleanRow.remove("rowNumber");  // 移除行号字段（非业务数据）
        
        // 对其他字符串字段进行JSON安全处理
        for (Map.Entry<String, Object> entry : cleanRow.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                // 使用JSON安全处理，防止编码错误
                String cleanValue = jsonSafeString((String) value);
                entry.setValue(cleanValue);
            }
        }
        
        return cleanRow;
    }
    
    /**
     * 构建业务逻辑唯一键
     * @param entity 实体对象
     * @return 业务逻辑唯一键
     */
    private String buildBusinessLogicKey(TransportRawBtsDealNew entity) {
        return String.format("%s_%s_%s_%s", 
                StringUtils.trimToEmpty(entity.getCellName()),
                StringUtils.trimToEmpty(entity.getCellId()),
                StringUtils.trimToEmpty(entity.getBtsName()),
                StringUtils.trimToEmpty(entity.getBtsId()));
    }
    
    /**
     * 批量查询历史错误记录
     * @param entities 实体列表
     * @return 业务逻辑键 -> 历史错误记录的映射
     */
    private Map<String, TransportRawBtsDealLogNew> batchQueryHistoricalErrors(List<TransportRawBtsDealNew> entities) {
        if (entities.isEmpty()) {
            return new HashMap<>();
        }
        
        try {
            List<TransportRawBtsDealLogNew> historicalErrors = transportRawBtsDealLogNewService.batchFindHistoricalErrorsByBusinessLogicKeys(entities);
            
            Map<String, TransportRawBtsDealLogNew> resultMap = new HashMap<>();
            for (TransportRawBtsDealLogNew error : historicalErrors) {
                // 从错误记录的deal_data中解析出业务逻辑键
                String businessKey = extractBusinessKeyFromDealData(error.getDealData());
                if (StringUtils.isNotBlank(businessKey)) {
                    resultMap.put(businessKey, error);
                }
            }
            
            return resultMap;
        } catch (Exception e) {
            // 如果批量查询失败，返回空Map，保证业务流程继续
            System.err.println("批量查询历史错误记录失败：" + e.getMessage());
            return new HashMap<>();
        }
    }
    
    /**
     * 批量查询现有错误记录
     * @param jobId 任务ID
     * @param entities 实体列表
     * @return 业务逻辑键 -> 现有错误记录的映射
     */
    private Map<String, TransportRawBtsDealLogNew> batchQueryExistingErrors(Long jobId, List<TransportRawBtsDealNew> entities) {
        if (entities.isEmpty()) {
            return new HashMap<>();
        }
        
        try {
            List<TransportRawBtsDealLogNew> existingErrors = transportRawBtsDealLogNewService.batchFindByBusinessLogicKeys(jobId, entities);
            
            Map<String, TransportRawBtsDealLogNew> resultMap = new HashMap<>();
            for (TransportRawBtsDealLogNew error : existingErrors) {
                // 从错误记录的deal_data中解析出业务逻辑键
                String businessKey = extractBusinessKeyFromDealData(error.getDealData());
                if (StringUtils.isNotBlank(businessKey)) {
                    resultMap.put(businessKey, error);
                }
            }
            
            return resultMap;
        } catch (Exception e) {
            // 如果批量查询失败，返回空Map，保证业务流程继续
            System.err.println("批量查询现有错误记录失败：" + e.getMessage());
            return new HashMap<>();
        }
    }
    
    /**
     * 从deal_data JSON中提取业务逻辑键
     * @param dealData JSON字符串
     * @return 业务逻辑键
     */
    private String extractBusinessKeyFromDealData(String dealData) {
        if (StringUtils.isBlank(dealData)) {
            return null;
        }
        
        try {
            JSONObject json = JSONObject.parseObject(dealData);
            String cellName = StringUtils.trimToEmpty(json.getString("cellName"));
            String cellId = StringUtils.trimToEmpty(json.getString("cellId"));
            String btsName = StringUtils.trimToEmpty(json.getString("btsName"));
            String btsId = StringUtils.trimToEmpty(json.getString("btsId"));
            
            return String.format("%s_%s_%s_%s", cellName, cellId, btsName, btsId);
        } catch (Exception e) {
            System.err.println("解析deal_data失败：" + e.getMessage());
            return null;
        }
    }

    private TransportSchedule convertToTransportSchedule(Map<String, Object> row,TransportJobNew transportJobNew,List<RegionDTO> orgRegionList,Map<String,String> tsMap) {
        TransportSchedule entity = new TransportSchedule();

        // 设置任务相关字段
        entity.setJobId(transportJobNew.getId());
        entity.setJobBranchId(getLongValue(row, "jobBranchId"));

        // 设置业务字段（根据CSV字段映射）
        entity.setCellName(getStringValue(row, "cellName"));
        entity.setCellId(getStringValue(row, "cellId"));
        entity.setBtsName(getStringValue(row, "btsName"));
        entity.setBtsId(getStringValue(row, "btsId"));
        entity.setTechType(getStringValue(row, "techType"));
        entity.setLocation(getStringValue(row, "location"));
        entity.setLongitude(getStringValue(row, "longitude"));
        entity.setLatitude(getStringValue(row, "latitude"));
        entity.setSendStartFreq(getStringValue(row, "sendStartFreq"));
        entity.setSendEndFreq(getStringValue(row, "sendEndFreq"));
        entity.setAccStartFreq(getStringValue(row, "accStartFreq"));
        entity.setAccEndFreq(getStringValue(row, "accEndFreq"));
        entity.setMaxEmissivePower(getStringValue(row, "maxEmissivePower"));
        entity.setHeight(getStringValue(row, "height"));
        entity.setVendorName(getStringValue(row, "deviceFactory"));
        entity.setDeviceModel(getStringValue(row, "deviceModel"));
        entity.setModelCode(getStringValue(row, "modelCode"));
        entity.setAntennaModel(getStringValue(row, "antennaModel"));
        entity.setAntennaFactory(getStringValue(row, "antennaFactory"));
        entity.setPolarizationMode(getStringValue(row, "polarizationMode"));
        entity.setAntennaAzimuth(getStringValue(row, "antennaAzimuth"));
        entity.setAtRang(getStringValue(row, "atRang"));
        entity.setAtEang(getStringValue(row, "atEang"));
        entity.setFeederLoss(getStringValue(row, "feedLose"));
        entity.setAntennaGain(getStringValue(row, "antGain"));
        entity.setAltitude(getStringValue(row, "altitude"));
        entity.setSetYear(getStringValue(row, "setYear"));
        entity.setSetMonth(getStringValue(row, "setMonth"));
        entity.setExpandStation(getStringValue(row, "expandStation"));
        entity.setAttributeStation(getStringValue(row, "attributeStation"));
        entity.setStServR(getStringValue(row, "stServR"));
        entity.setCounty(getStringValue(row, "county"));
        if ("2".equals(transportJobNew.getApplyType())){
            entity.setDataType("3");
            entity.setBtsDataType("3");
        }else if ("3".equals(transportJobNew.getApplyType())){
            entity.setDataType("4");
            entity.setBtsDataType("4");
        }
        entity.setRegionCode(getStringValue(row, "regionCode"));
        entity.setGenNum(tsMap.get(entity.getTechType()));
        entity.setOrgType(transportJobNew.getOperatorCode());
        entity.setStScene(getStringValue(row, "stScene"));
        entity.setTrfUser(getDoubleValue(row, "trfUser"));
        entity.setTrfData(getDoubleValue(row, "trfData"));
        entity.setTrfDate(getDateValue(row, "trfDate"));
        entity.setIsValid(1L);

        return entity;
    }

    /**
     * 更新最终任务状态
     * @param job 任务对象
     * @param processingResult 处理结果
     */
    private void updateFinalTaskStatus(TransportJobNew job, Map<String, Object> processingResult) {
        boolean hasProcessingError = (Boolean) processingResult.get("hasProcessingError");
        
        // 检查是否还有错误记录
        List<TransportRawBtsDealLogNew> errorLogs = transportRawBtsDealLogNewService.listByJobId(job.getId());
        boolean hasErrorLogs = !errorLogs.isEmpty();

        // 状态流转
        if (hasProcessingError) {
            // 有文件处理失败
            job.setStatus("invalid_data");
        } else if (hasErrorLogs) {
            // 文件处理成功但有数据错误
            job.setStatus("invalid_data");
        } else {
            // 所有数据都正确
            job.setStatus("checked");
        }
        
        job.setUpdatedAt(new Date());
        transportJobNewService.update(job);
    }

    /**
     * 任务情况查询（分页）
     * @param dto 查询条件
     * @param token 用户token
     * @return 查询结果
     */
    public Map<String, Object> findJobsWithConditions(TransportJobNewSearchVO dto, String token) {
        try {
            // 根据token获取用户信息
            UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
            if (usersDTO == null) {
                return this.basicReturnFailure("用户未登录或token无效");
            }
            
            // 设置权限限制：用户只能查看自己提交的任务，但admin用户可以查看所有任务
            if (!"admin".equals(usersDTO.getName())) {
                dto.setCreatedBy(usersDTO.getName());
            }
            // 如果是admin用户，不设置createdBy限制，可以查看所有任务
            
            return this.basicReturnResultJson(new PageHandle(dto).buildPage(
                    transportJobNewService.findPageWithConditions(dto)));
        } catch (Exception e) {
            return this.basicReturnFailure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 任务详情查询
     * @param jobId 任务ID
     * @return 任务详情
     */
    public Map<String, Object> getJobDetail(Long jobId) {
        try {
            TransportJobNew job = transportJobNewService.findById(jobId);
            if (job == null) {
                return this.basicReturnFailure("任务不存在");
            }

            // 创建任务详情DTO
            TransportJobDetailDTO jobDetailDTO = new TransportJobDetailDTO();

            // 转换任务信息，所有ID字段转为String
            TransportJobNewDTO jobInfoDTO = convertToJobInfoDTO(job);
            jobDetailDTO.setJob(jobInfoDTO);

            // 获取并转换关联文件
            List<TransportFile> files = transportFileService.findByJobId(jobId);
            List<FileInfoDTO> fileInfoDTOs = convertToFileInfoDTOs(files);
            jobDetailDTO.setFiles(fileInfoDTOs);

            // 获取文件统计信息
            List<TransportFileDTO> fileStats = transportFileService.countByJobIdGroupByFileType(jobId);
            jobDetailDTO.setFileStats(fileStats);

            // 获取数据统计信息
            DataStatsDTO dataStatsDTO = getDataStatsByJobId(jobId);
            jobDetailDTO.setDataStats(dataStatsDTO);

            return this.basicReturnSuccess(jobDetailDTO);
        } catch (Exception e) {
            return this.basicReturnFailure("查询失败：" + e.getMessage());
        }
    }


    /**
     * 获取任务状态统计
     * @return 统计结果
     */
    public Map<String, Object> getJobStatusStatistics() {
        try {
            List<TransportJobNewSearchVO> statistics = transportJobNewService.countByStatus();
            return this.basicReturnSuccess(statistics);
        } catch (Exception e) {
            return this.basicReturnFailure("统计失败：" + e.getMessage());
        }
    }

    /**
     * 根据创建人查询任务列表
     * @param createdBy 创建人
     * @return 任务列表
     */
    public Map<String, Object> getJobsByCreatedBy(String createdBy) {
        try {
            List<TransportJobNewSearchVO> jobs = transportJobNewService.findByCreatedBy(createdBy);
            return this.basicReturnSuccess(jobs);
        } catch (Exception e) {
            return this.basicReturnFailure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 将Map数据转换为TransportRawBtsDealNew实体
     * @param row 数据行
     * @param jobId 任务ID
     * @return 实体对象
     */
    private TransportRawBtsDealNew convertToTransportRawBtsDealNew(Map<String, Object> row, Long jobId) {
        TransportRawBtsDealNew entity = new TransportRawBtsDealNew();

        // 设置任务相关字段
        entity.setJobId(jobId);
        entity.setJobBranchId(getLongValue(row, "jobBranchId"));

        // 设置业务字段（根据CSV字段映射）
        entity.setCellName(getStringValue(row, "cellName"));
        entity.setCellId(getStringValue(row, "cellId"));
        entity.setBtsName(getStringValue(row, "btsName"));
        entity.setBtsId(getStringValue(row, "btsId"));
        entity.setTechType(getStringValue(row, "techType"));
        entity.setLocation(getStringValue(row, "location"));
        entity.setCounty(getStringValue(row, "county"));
        entity.setLongitude(getStringValue(row, "longitude"));
        entity.setLatitude(getStringValue(row, "latitude"));
        entity.setSendStartFreq(getStringValue(row, "sendStartFreq"));
        entity.setSendEndFreq(getStringValue(row, "sendEndFreq"));
        entity.setAccStartFreq(getStringValue(row, "accStartFreq"));
        entity.setAccEndFreq(getStringValue(row, "accEndFreq"));
        entity.setMaxEmissivePower(getStringValue(row, "maxEmissivePower"));
        entity.setHeight(getStringValue(row, "height"));
        entity.setDeviceFactory(getStringValue(row, "deviceFactory"));
        entity.setDeviceModel(getStringValue(row, "deviceModel"));
        entity.setModelCode(getStringValue(row, "modelCode"));
        entity.setAntennaModel(getStringValue(row, "antennaModel"));
        entity.setAntennaFactory(getStringValue(row, "antennaFactory"));
        entity.setPolarizationMode(getStringValue(row, "polarizationMode"));
        entity.setAntennaAzimuth(getStringValue(row, "antennaAzimuth"));
        entity.setAtRang(getStringValue(row, "atRang"));
        entity.setAtEang(getStringValue(row, "atEang"));
        entity.setFeederLoss(getStringValue(row, "feedLose"));
        entity.setAntennaGain(getStringValue(row, "antGain"));
        entity.setAltitude(getStringValue(row, "altitude"));
        entity.setSetYear(getStringValue(row, "setYear"));
        entity.setSetMonth(getStringValue(row, "setMonth"));
        entity.setExpandStation(getStringValue(row, "expandStation"));
        entity.setAttributeStation(getStringValue(row, "attributeStation"));
        entity.setStServR(getStringValue(row, "stServR"));
        entity.setStScene(getStringValue(row, "stScene"));
        entity.setTrfUser(getDoubleValue(row, "trfUser"));
        entity.setTrfData(getDoubleValue(row, "trfData"));
        entity.setTrfDate(getDateValue(row, "trfDate"));

        // 设置时间字段
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);

        return entity;
    }

    /**
     * 将Map数据转换为TransportRawBtsDealLogNew实体
     * @param row 数据行
     * @param jobId 任务ID
     * @param errorMessage 错误信息
     * @return 日志实体对象
     */
    private TransportRawBtsDealLogNew convertToTransportRawBtsDealLogNew(Map<String, Object> row, Long jobId, String errorMessage) {
        TransportRawBtsDealLogNew logEntity = new TransportRawBtsDealLogNew();

        // 设置任务相关字段
        logEntity.setJobId(jobId);

        // 解析错误信息，提取字段名
        String fieldName = extractFieldNameFromError(errorMessage);
        logEntity.setFieldName(fieldName);

        // 设置错误相关字段
        String errorType = getStringValue(row, "errorType");
        if (StringUtils.isNotBlank(errorType)) {
            logEntity.setErrorType(errorType);
        } else {
            logEntity.setErrorType("VALIDATION_ERROR");
        }
        
        // 对错误信息进行 SQL 安全处理
        logEntity.setErrorMessage(sqlSafeString(errorMessage));
        logEntity.setOriginalValue(getOriginalValueFromError(row, fieldName));

        // 清理行数据中可能导致 SQL 和 JSON 问题的字段，然后序列化为JSON存储
        Map<String, Object> cleanRow = cleanRowDataForLog(row);
        
        logEntity.setDealData(JSON.toJSONString(cleanRow));
        logEntity.setCreatedAt(new Date());

        return logEntity;
    }
    
    /**
     * SQL 安全字符串处理，防止单引号导致的 SQL 语法错误
     * @param input 输入字符串
     * @return 安全处理后的字符串
     */
    private String sqlSafeString(String input) {
        if (input == null) {
            return null;
        }
        // 将单引号替换为两个单引号（SQL 转义）
        // 同时处理其他可能导致问题的特殊字符
        return input.replace("'", "''")
                   .replace("\\", "\\\\")
                   .replace("\"", "\\\"");
    }

    /**
     * JSON 安全字符串处理，防止非UTF-8字符和JSON不兼容字符导致的编码错误
     * @param input 输入字符串
     * @return 安全处理后的字符串
     */
    private String jsonSafeString(String input) {
        if (input == null) {
            return null;
        }
        
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            
            // 处理控制字符和非打印字符
            if (c < 32 && c != '\t' && c != '\n' && c != '\r') {
                // 跳过或替换控制字符（除了tab、换行、回车）
                continue;
            }
            
            // 处理特殊的Unicode字符
            if (Character.isISOControl(c)) {
                // 跳过ISO控制字符
                continue;
            }
            
            // 处理代理对（高代理和低代理）
            if (Character.isHighSurrogate(c)) {
                if (i + 1 < input.length() && Character.isLowSurrogate(input.charAt(i + 1))) {
                    // 有效的代理对，保留
                    result.append(c).append(input.charAt(i + 1));
                    i++; // 跳过下一个字符
                }
                // 无效的高代理，跳过
                continue;
            }
            
            if (Character.isLowSurrogate(c)) {
                // 孤立的低代理，跳过
                continue;
            }
            
            // 处理其他可能导致JSON问题的字符
            switch (c) {
                case '\u0000': // NULL字符
                case '\uFFFD': // 替换字符（通常表示编码错误）
                    continue; // 跳过这些字符
                default:
                    result.append(c);
                    break;
            }
        }
        
        return result.toString();
    }

    /**
     * 从错误信息中提取字段名
     * @param errorMessage 错误信息
     * @return 字段名
     */
    private String extractFieldNameFromError(String errorMessage) {
        if (StringUtils.isBlank(errorMessage)) {
            return "UNKNOWN";
        }

        // 检查是否为基站级错误
        if (errorMessage.contains("同基站下某扇区校验出错")) {
            return "BTS_ID"; // 基站级错误关联到基站ID字段
        }

        // 检查基站级公共参数一致性错误
        if (errorMessage.contains("同基站下非拉远站的") && errorMessage.contains("必须一致")) {
            // 从错误信息中提取具体的字段名
            // 格式："同基站下非拉远站的[字段名]必须一致，期望值：xxx，实际值：xxx"
            if (errorMessage.contains("基站名称")) {
                return "BTS_NAME";
            } else if (errorMessage.contains("基站识别码")) {
                return "BTS_ID";
            } else if (errorMessage.contains("技术类型")) {
                return "TECH_TYPE";
            } else if (errorMessage.contains("位置")) {
                return "LOCATION";
            } else if (errorMessage.contains("区县")) {
                return "COUNTY";
            } else if (errorMessage.contains("经度")) {
                return "LONGITUDE";
            } else if (errorMessage.contains("纬度")) {
                return "LATITUDE";
            } else if (errorMessage.contains("海拔高度")) {
                return "ALTITUDE";
            }
            // 如果无法识别具体字段，返回通用的基站ID
            return "BTS_ID";
        }

        // 检查频率范围校验错误
        if (errorMessage.contains("频率超出合法范围") || errorMessage.contains("频率校验规则") || 
            errorMessage.contains("频率必须小于") || errorMessage.contains("频率范围校验") ||
            errorMessage.contains("未同时满足任一组合法频率范围") || errorMessage.contains("未满足合法频率范围")) {
            
            // 更精确的错误字段判断
            if (errorMessage.contains("发射频率未满足合法频率范围")) {
                return "SEND_START_FREQ"; // 只有发射频率有问题
            } else if (errorMessage.contains("接收频率未满足合法频率范围")) {
                return "ACC_START_FREQ"; // 只有接收频率有问题
            } else if (errorMessage.contains("发射频率和接收频率均未满足")) {
                return "SEND_START_FREQ"; // 发射和接收频率都有问题时，返回发射起始频率字段
            } else if (errorMessage.contains("发射频率和接收频率未同时满足")) {
                return "SEND_START_FREQ"; // 组合不匹配时，默认返回发射起始频率字段
            } else if (errorMessage.contains("发射频率")) {
                return "SEND_START_FREQ"; // 包含发射频率的其他错误
            } else if (errorMessage.contains("接收频率")) {
                return "ACC_START_FREQ"; // 包含接收频率的其他错误
            } else {
                return "TECH_TYPE"; // 通用的技术体制字段
            }
        }

        // 核心表存在性错误（延续/注销操作）
        if (errorMessage.contains("该扇区不存在，无法执行延续/注销操作")) {
            return "CELL_NAME"; // 扇区名称
        }

        // 系统错误：无法验证扇区是否存在
        if (errorMessage.contains("系统错误：无法验证扇区是否存在")) {
            return "CELL_NAME"; // 扇区名称
        }

        // 尝试从错误信息中提取字段名（格式：字段'FIELD_NAME'xxx）
        if (errorMessage.contains("字段'") && errorMessage.contains("'")) {
            int start = errorMessage.indexOf("字段'") + 3;
            int end = errorMessage.indexOf("'", start);
            if (end > start) {
                return errorMessage.substring(start, end);
            }
        }

        return "UNKNOWN";
    }

    /**
     * 获取出错字段的原始值
     * @param row 数据行
     * @param fieldName 字段名
     * @return 原始值
     */
    private String getOriginalValueFromError(Map<String, Object> row, String fieldName) {
        if ("UNKNOWN".equals(fieldName) || row == null) {
            return "";
        }

        // 特殊处理：从错误信息中直接提取实际值（针对基站级公共参数错误和频率错误）
        @SuppressWarnings("unchecked")
        List<String> errors = (List<String>) row.get("errors");
        if (errors != null && !errors.isEmpty()) {
            for (String error : errors) {
                if (error.contains("同基站下非拉远站的") && error.contains("必须一致") && error.contains("实际值：")) {
                    // 从错误信息中提取实际值
                    // 格式："同基站下非拉远站的[字段名]必须一致，期望值：xxx，实际值：xxx"
                    int actualIndex = error.indexOf("实际值：");
                    if (actualIndex != -1) {
                        String actualValue = error.substring(actualIndex + 4); // "实际值：".length() = 4
                        return jsonSafeString(actualValue);
                    }
                }
                
                // 处理频率范围错误
                if (error.contains("频率超出合法范围") || error.contains("频率校验规则") || 
                    error.contains("未同时满足任一组合法频率范围") || error.contains("未满足合法频率范围")) {
                    // 根据具体的错误类型和字段名返回相应的频率值
                    if ("SEND_START_FREQ".equals(fieldName)) {
                        Object sendStart = row.get("sendStartFreq");
                        Object sendEnd = row.get("sendEndFreq");
                        String result = String.format("%s-%s", 
                                sendStart != null ? sendStart.toString() : "", 
                                sendEnd != null ? sendEnd.toString() : "");
                        return jsonSafeString(result);
                    } else if ("ACC_START_FREQ".equals(fieldName)) {
                        Object accStart = row.get("accStartFreq");
                        Object accEnd = row.get("accEndFreq");
                        String result = String.format("%s-%s", 
                                accStart != null ? accStart.toString() : "", 
                                accEnd != null ? accEnd.toString() : "");
                        return jsonSafeString(result);
                    } else if ("TECH_TYPE".equals(fieldName)) {
                        Object techType = row.get("techType");
                        String result = techType != null ? techType.toString() : "";
                        return jsonSafeString(result);
                    }
                }
            }
        }

        // 根据字段名获取对应的值
        String entityFieldName = CSV_FIELD_MAPPING.get(fieldName);
        if (entityFieldName != null && row.containsKey(entityFieldName)) {
            Object value = row.get(entityFieldName);
            String stringValue = value != null ? value.toString() : "";
            // 应用JSON安全处理
            return jsonSafeString(stringValue);
        }

        return "";
    }

    /**
     * 安全获取字符串值
     * @param row 数据行
     * @param key 键名
     * @return 字符串值
     */
    private String getStringValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        return value != null ? value.toString().trim() : null;
    }

    /**
     * 安全获取Long值
     * @param row 数据行
     * @param key 键名
     * @return Long值
     */
    private Long getLongValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Long) {
            return (Long) value;
        }

        if (value instanceof Number) {
            return ((Number) value).longValue();
        }

        try {
            return Long.parseLong(value.toString().trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取Integer值
     * @param row 数据行
     * @param key 键名
     * @return Integer值
     */
    private Integer getIntegerValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Integer) {
            return (Integer) value;
        }

        if (value instanceof Number) {
            return ((Number) value).intValue();
        }

        try {
            return Integer.parseInt(value.toString().trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取BigDecimal值
     * @param row 数据行
     * @param key 键名
     * @return BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }

        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }

        try {
            String strValue = value.toString().trim();
            if (StringUtils.isBlank(strValue)) {
                return null;
            }
            return new BigDecimal(strValue);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取Double值
     * @param row 数据行
     * @param key 键名
     * @return Double值
     */
    private Double getDoubleValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Double) {
            return (Double) value;
        }

        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }

        try {
            String strValue = value.toString().trim();
            if (StringUtils.isBlank(strValue)) {
                return null;
            }
            return Double.parseDouble(strValue);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取Date值（专门用于TRF_DATE字段，只保留日期部分）
     * @param row 数据行
     * @param key 键名
     * @return Date值
     */
    private Date getDateValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Date) {
            // 如果已经是Date对象，移除时分秒部分，只保留日期
            return removeTimeFromDate((Date) value);
        }

        try {
            String strValue = value.toString().trim();
            if (StringUtils.isBlank(strValue)) {
                return null;
            }
            
            // 尝试多种日期格式解析
            String[] datePatterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd",
                "yyyy/MM/dd HH:mm:ss",
                "yyyy/MM/dd",
                "yyyyMMdd",
                "yyyy-MM"
            };
            
            for (String pattern : datePatterns) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    Date parsedDate = sdf.parse(strValue);
                    // 移除时分秒部分，只保留日期
                    return removeTimeFromDate(parsedDate);
                } catch (ParseException e) {
                    // 继续尝试下一个格式
                }
            }
            
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 移除Date对象的时分秒部分，只保留日期
     * @param date 原始日期
     * @return 只包含日期的Date对象
     */
    private Date removeTimeFromDate(Date date) {
        if (date == null) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = sdf.format(date);
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            return date; // 如果解析失败，返回原始日期
        }
    }

    /**
     * 根据任务ID查询数据详情（正确数据和错误数据的统一查询）
     * @param vo 查询条件DTO（包含jobId和分页信息）
     * @return 分页查询结果
     * 不使用公共分页方法的原因是结果太复杂
     */
    public Map<String, Object> getDataDetailByJobId(DataDetailVO vo) {
        try {
            // 从vo中获取jobId和分页信息
            Long jobId = vo.getJobId();
            if (jobId == null) {
                return this.basicReturnFailure("任务ID不能为空");
            }

            int page = vo.getPage();
            int rows = vo.getRows();
            if (page <= 0) page = 1;
            if (rows <= 0) rows = 10;

            // 计算分页参数
            int offset = (page - 1) * rows;

            // 获取总数用于分页
            long validCount = transportRawBtsDealNewService.countByJobId(jobId);
            long invalidCount = transportRawBtsDealLogNewService.countByJobId(jobId);
            long totalCount = validCount + invalidCount;

            // 转换数据为统一vo格式
            List<DataDetailDTO> resultList = new ArrayList<>();

            // 分页逻辑：先取正确数据，再取错误数据
            int currentCount = 0;
            int remainingRows = rows;

            // 处理正确数据
            if (offset < validCount && remainingRows > 0) {
                int validOffset = offset;
                int validLimit = Math.min(remainingRows, (int)(validCount - offset));
                
                List<TransportRawBtsDealNew> validDataList = transportRawBtsDealNewService.findByJobIdWithPaging(jobId, validOffset, validLimit);
                
                for (TransportRawBtsDealNew validData : validDataList) {
                    DataDetailDTO dataDto = convertToDataDetailDTO(validData, "valid");
                    resultList.add(dataDto);
                    currentCount++;
                    remainingRows--;
                }
            }

            // 处理错误数据
            if (remainingRows > 0) {
                int invalidOffset = Math.max(0, offset - (int)validCount);
                int invalidLimit = remainingRows;
                
                List<TransportRawBtsDealLogNew> invalidDataList = transportRawBtsDealLogNewService.findByJobIdWithPaging(jobId, invalidOffset, invalidLimit);
                
                for (TransportRawBtsDealLogNew invalidData : invalidDataList) {
                    DataDetailDTO dataDto = convertToDataDetailDTO(invalidData, "invalid");
                    resultList.add(dataDto);
                    currentCount++;
                }
            }

            // 构建分页结果
            Map<String, Object> pageResult = new HashMap<>();
            pageResult.put("rows", resultList);
            pageResult.put("page", page);
            pageResult.put("total", totalCount);
            pageResult.put("validCount", validCount);
            pageResult.put("invalidCount", invalidCount);

            return this.basicReturnSuccess(pageResult);

        } catch (Exception e) {
            return this.basicReturnFailure("查询数据详情失败：" + e.getMessage());
        }
    }

    /**
     * 将TransportRawBtsDealNew转换为DataDetailDTO
     * @param validData 正确数据实体
     * @param dataType 数据类型
     * @return DTO对象
     */
    private DataDetailDTO convertToDataDetailDTO(TransportRawBtsDealNew validData, String dataType) {
        DataDetailDTO dto = new DataDetailDTO();
        dto.setDataType(dataType);
        dto.setErrorMessage(null); // 正确数据没有错误信息

        // 设置基站相关业务数据
        dto.setCellName(validData.getCellName());
        dto.setCellId(validData.getCellId());
        dto.setBtsName(validData.getBtsName());
        dto.setBtsId(validData.getBtsId());
        dto.setTechType(validData.getTechType());
        dto.setLocation(validData.getLocation());
        dto.setCounty(validData.getCounty());
        dto.setLongitude(validData.getLongitude());
        dto.setLatitude(validData.getLatitude());
        dto.setSendStartFreq(validData.getSendStartFreq());
        dto.setSendEndFreq(validData.getSendEndFreq());
        dto.setAccStartFreq(validData.getAccStartFreq());
        dto.setAccEndFreq(validData.getAccEndFreq());
        dto.setMaxEmissivePower(validData.getMaxEmissivePower());
        dto.setHeight(validData.getHeight());
        dto.setDeviceFactory(validData.getDeviceFactory());
        dto.setDeviceModel(validData.getDeviceModel());
        dto.setModelCode(validData.getModelCode());
        dto.setAntennaModel(validData.getAntennaModel());
        dto.setAntennaFactory(validData.getAntennaFactory());
        dto.setPolarizationMode(validData.getPolarizationMode());
        dto.setAntennaAzimuth(validData.getAntennaAzimuth());
        dto.setAtRang(validData.getAtRang());
        dto.setAtEang(validData.getAtEang());
        dto.setFeederLoss(validData.getFeederLoss());
        dto.setAntennaGain(validData.getAntennaGain());
        dto.setAltitude(validData.getAltitude());
        dto.setSetYear(validData.getSetYear());
        dto.setSetMonth(validData.getSetMonth());
        dto.setExpandStation(validData.getExpandStation());
        dto.setAttributeStation(validData.getAttributeStation());
        dto.setStServR(validData.getStServR());
        dto.setStScene(validData.getStScene());
        dto.setTrfDate(validData.getTrfDate());
        dto.setTrfUser(validData.getTrfUser());
        dto.setTrfData(validData.getTrfData());
        dto.setCreateTime(validData.getCreateTime());

        return dto;
    }

    /**
     * 将TransportRawBtsDealLogNew转换为DataDetailDTO
     * @param invalidData 错误数据实体
     * @param dataType 数据类型
     * @return DTO对象
     */
    private DataDetailDTO convertToDataDetailDTO(TransportRawBtsDealLogNew invalidData, String dataType) {
        DataDetailDTO dto = new DataDetailDTO();
        dto.setDataType(dataType);
        dto.setErrorMessage(invalidData.getErrorMessage());

        // 解析deal_data JSON字符串
        if (StringUtils.isNotBlank(invalidData.getDealData())) {
            try {
                JSONObject dealDataJson = JSONObject.parseObject(invalidData.getDealData());
                
                // 从JSON中提取基站相关业务数据
                dto.setCellName(getStringFromJson(dealDataJson, "cellName"));
                dto.setCellId(getStringFromJson(dealDataJson, "cellId"));
                dto.setBtsName(getStringFromJson(dealDataJson, "btsName"));
                dto.setBtsId(getStringFromJson(dealDataJson, "btsId"));
                dto.setTechType(getStringFromJson(dealDataJson, "techType"));
                dto.setLocation(getStringFromJson(dealDataJson, "location"));
                dto.setCounty(getStringFromJson(dealDataJson, "county"));
                dto.setLongitude(getStringFromJson(dealDataJson, "longitude"));
                dto.setLatitude(getStringFromJson(dealDataJson, "latitude"));
                dto.setSendStartFreq(getStringFromJson(dealDataJson, "sendStartFreq"));
                dto.setSendEndFreq(getStringFromJson(dealDataJson, "sendEndFreq"));
                dto.setAccStartFreq(getStringFromJson(dealDataJson, "accStartFreq"));
                dto.setAccEndFreq(getStringFromJson(dealDataJson, "accEndFreq"));
                dto.setMaxEmissivePower(getStringFromJson(dealDataJson, "maxEmissivePower"));
                dto.setHeight(getStringFromJson(dealDataJson, "height"));
                dto.setDeviceFactory(getStringFromJson(dealDataJson, "deviceFactory"));
                dto.setDeviceModel(getStringFromJson(dealDataJson, "deviceModel"));
                dto.setModelCode(getStringFromJson(dealDataJson, "modelCode"));
                dto.setAntennaModel(getStringFromJson(dealDataJson, "antennaModel"));
                dto.setAntennaFactory(getStringFromJson(dealDataJson, "antennaFactory"));
                dto.setPolarizationMode(getStringFromJson(dealDataJson, "polarizationMode"));
                dto.setAntennaAzimuth(getStringFromJson(dealDataJson, "antennaAzimuth"));
                dto.setAtRang(getStringFromJson(dealDataJson, "atRang"));
                dto.setAtEang(getStringFromJson(dealDataJson, "atEang"));
                dto.setFeederLoss(getStringFromJson(dealDataJson, "feedLose"));
                dto.setAntennaGain(getStringFromJson(dealDataJson, "antGain"));
                dto.setAltitude(getStringFromJson(dealDataJson, "altitude"));
                dto.setSetYear(getStringFromJson(dealDataJson, "setYear"));
                dto.setSetMonth(getStringFromJson(dealDataJson, "setMonth"));
                dto.setExpandStation(getStringFromJson(dealDataJson, "expandStation"));
                dto.setAttributeStation(getStringFromJson(dealDataJson, "attributeStation"));
                dto.setStServR(getStringFromJson(dealDataJson, "stServR"));
                dto.setStScene(getStringFromJson(dealDataJson, "stScene"));
                dto.setTrfDate(getDateFromJson(dealDataJson, "trfDate"));
                BigDecimal trfUserBd = getBigDecimalFromJson(dealDataJson, "trfUser");
                dto.setTrfUser(trfUserBd != null ? trfUserBd.doubleValue() : null);
                BigDecimal trfDataBd = getBigDecimalFromJson(dealDataJson, "trfData");
                dto.setTrfData(trfDataBd != null ? trfDataBd.doubleValue() : null);

            } catch (Exception e) {
                // JSON解析失败时设置默认值
                System.err.println("解析deal_data JSON失败：" + e.getMessage());
            }
        }

        dto.setCreateTime(invalidData.getCreatedAt());
        return dto;
    }

    /**
     * 从JSON对象中安全获取字符串值
     * @param jsonObject JSON对象
     * @param key 键名
     * @return 字符串值
     */
    private String getStringFromJson(JSONObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }
        Object value = jsonObject.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 从JSON对象中安全获取BigDecimal值
     * @param jsonObject JSON对象
     * @param key 键名
     * @return BigDecimal值
     */
    private BigDecimal getBigDecimalFromJson(JSONObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }
        Object value = jsonObject.get(key);
        if (value == null) {
            return null;
        }
        try {
            if (value instanceof BigDecimal) {
                return (BigDecimal) value;
            }
            if (value instanceof Number) {
                return BigDecimal.valueOf(((Number) value).doubleValue());
            }
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从JSON对象中安全获取Integer值
     * @param jsonObject JSON对象
     * @param key 键名
     * @return Integer值
     */
    private Integer getIntegerFromJson(JSONObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }
        Object value = jsonObject.get(key);
        if (value == null) {
            return null;
        }
        try {
            if (value instanceof Integer) {
                return (Integer) value;
            }
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            return Integer.parseInt(value.toString());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从JSON对象中安全获取Date值（专门用于TRF_DATE字段，只保留日期部分）
     * @param jsonObject JSON对象
     * @param key 键名
     * @return Date值
     */
    private Date getDateFromJson(JSONObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }

        Object value = jsonObject.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Date) {
            // 如果已经是Date对象，移除时分秒部分，只保留日期
            return removeTimeFromDate((Date) value);
        }

        try {
            String strValue = value.toString().trim();
            if (StringUtils.isBlank(strValue)) {
                return null;
            }
            
            // 尝试多种日期格式解析
            String[] datePatterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd",
                "yyyy/MM/dd HH:mm:ss",
                "yyyy/MM/dd",
                "yyyyMMdd",
                "yyyy-MM"
            };
            
            for (String pattern : datePatterns) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    Date parsedDate = sdf.parse(strValue);
                    // 移除时分秒部分，只保留日期
                    return removeTimeFromDate(parsedDate);
                } catch (ParseException e) {
                    // 继续尝试下一个格式
                }
            }
            
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据任务ID下载数据详情为CSV文件
     * @param jobId 任务ID
     * @return 下载结果信息
     */
    public Map<String, Object> downloadDataDetailByJobId(Long jobId) {
        try {
            if (jobId == null) {
                return this.basicReturnFailure("任务ID不能为空");
            }

            // 确保导出目录存在
            File exportDir = new File(myFilePath + "/export");
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }

            // 生成导出文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String timestamp = sdf.format(new Date());
            String fileName = "data_detail_" + jobId + "_" + timestamp + ".csv";
            String filePath = myFilePath + "/export/" + fileName;

            // 获取所有数据（不分页）
            List<TransportRawBtsDealNew> validDataList = transportRawBtsDealNewService.findByJobIdWithPaging(jobId, 0, Integer.MAX_VALUE);
            List<TransportRawBtsDealLogNew> invalidDataList = transportRawBtsDealLogNewService.findByJobIdWithPaging(jobId, 0, Integer.MAX_VALUE);

            // 写入CSV文件
            try (BufferedWriter writer = new BufferedWriter(
                    new OutputStreamWriter(new FileOutputStream(filePath), StandardCharsets.UTF_8))) {
                
                // 写入BOM标记，解决Excel打开CSV中文乱码问题
                writer.write('\ufeff');
                
                // 写入CSV表头
                writeCSVHeader(writer);
                
                // 写入正确数据
                for (TransportRawBtsDealNew validData : validDataList) {
                    writeValidDataToCSV(writer, validData);
                }
                
                // 写入错误数据
                for (TransportRawBtsDealLogNew invalidData : invalidDataList) {
                    writeInvalidDataToCSV(writer, invalidData);
                }
                
                writer.flush();
            }

            // 返回下载文件信息
            Map<String, Object> result = new HashMap<>();
            result.put("fileName", fileName);
            result.put("filePath", "/export/" + fileName);
            result.put("fileSize", new File(filePath).length());
            result.put("validCount", validDataList.size());
            result.put("invalidCount", invalidDataList.size());
            result.put("totalCount", validDataList.size() + invalidDataList.size());
            
            return this.basicReturnSuccess(result);

        } catch (Exception e) {
            return this.basicReturnFailure("导出数据详情失败：" + e.getMessage());
        }
    }

    /**
     * 写入CSV表头
     * @param writer 写入器
     * @throws IOException IO异常
     */
    private void writeCSVHeader(BufferedWriter writer) throws IOException {
        String[] headers = {
            "数据类型", "错误信息", "错误字段", "错误原值", "扇区名称", "扇区识别码", "台站名称", "台站识别码", "技术体制", 
            "台址", "行政区", "经度", "纬度", "发射起始频率", "发射终止频率", "接收起始频率", 
            "接收终止频率", "最大发射功率", "天线高度", "设备厂家", "设备型号", "型号核准代码",
            "天线型号", "天线厂家", "极化方式", "天线方位角", "水平半功率角", "垂直半功率角",
            "馈线损耗", "天线增益", "海拔高度", "设置年份", "设置月份", "扩建台站", "台站属性", "服务半径", 
            "基站覆盖场景", "用户数或流量统计年份月份", "平均忙时激活用户数", "平均忙时激活用户流量", "创建时间"
        };
        
        writer.write(String.join(",", headers));
        writer.newLine();
    }

    /**
     * 将正确数据写入CSV
     * @param writer 写入器
     * @param data 正确数据
     * @throws IOException IO异常
     */
    private void writeValidDataToCSV(BufferedWriter writer, TransportRawBtsDealNew data) throws IOException {
        String[] values = {
            "正确数据",
            "", // 错误信息为空
            "", // 错误字段为空
            "", // 错误原值为空
            escapeCSV(data.getCellName()),
            escapeCSV(data.getCellId()),
            escapeCSV(data.getBtsName()),
            escapeCSV(data.getBtsId()),
            escapeCSV(data.getTechType()),
            escapeCSV(data.getLocation()),
            escapeCSV(data.getCounty()),
            escapeCSV(data.getLongitude()),
            escapeCSV(data.getLatitude()),
            escapeCSV(data.getSendStartFreq()),
            escapeCSV(data.getSendEndFreq()),
            escapeCSV(data.getAccStartFreq()),
            escapeCSV(data.getAccEndFreq()),
            escapeCSV(data.getMaxEmissivePower()),
            escapeCSV(data.getHeight()),
            escapeCSV(data.getDeviceFactory()),
            escapeCSV(data.getDeviceModel()),
            escapeCSV(data.getModelCode()),
            escapeCSV(data.getAntennaModel()),
            escapeCSV(data.getAntennaFactory()),
            escapeCSV(data.getPolarizationMode()),
            escapeCSV(data.getAntennaAzimuth()),
            escapeCSV(data.getAtRang()),
            escapeCSV(data.getAtEang()),
            escapeCSV(data.getFeederLoss()),
            escapeCSV(data.getAntennaGain()),
            escapeCSV(data.getAltitude()),
            escapeCSV(data.getSetYear()),
            escapeCSV(data.getSetMonth()),
            escapeCSV(data.getExpandStation()),
            escapeCSV(data.getAttributeStation()),
            escapeCSV(data.getStServR()),
            escapeCSV(data.getStScene()),
            formatDate(data.getTrfDate()),
            formatDecimal(data.getTrfUser() != null ? BigDecimal.valueOf(data.getTrfUser()) : null),
            formatDecimal(data.getTrfData() != null ? BigDecimal.valueOf(data.getTrfData()) : null),
            formatDate(data.getCreateTime())
        };
        
        writer.write(String.join(",", values));
        writer.newLine();
    }

    /**
     * 将错误数据写入CSV
     * @param writer 写入器
     * @param data 错误数据
     * @throws IOException IO异常
     */
    private void writeInvalidDataToCSV(BufferedWriter writer, TransportRawBtsDealLogNew data) throws IOException {
        // 解析deal_data JSON字符串
        JSONObject dealDataJson = null;
        if (StringUtils.isNotBlank(data.getDealData())) {
            try {
                dealDataJson = JSONObject.parseObject(data.getDealData());
            } catch (Exception e) {
                // JSON解析失败，使用空对象
                dealDataJson = new JSONObject();
            }
        } else {
            dealDataJson = new JSONObject();
        }

        String[] values = {
            "错误数据",
            escapeCSV(data.getErrorMessage()),
            escapeCSV(data.getFieldName()), // 错误字段名
            escapeCSV(data.getOriginalValue()), // 错误原值
            escapeCSV(getStringFromJson(dealDataJson, "cellName")),
            escapeCSV(getStringFromJson(dealDataJson, "cellId")),
            escapeCSV(getStringFromJson(dealDataJson, "btsName")),
            escapeCSV(getStringFromJson(dealDataJson, "btsId")),
            escapeCSV(getStringFromJson(dealDataJson, "techType")),
            escapeCSV(getStringFromJson(dealDataJson, "location")),
            escapeCSV(getStringFromJson(dealDataJson, "county")),
            escapeCSV(getStringFromJson(dealDataJson, "longitude")),
            escapeCSV(getStringFromJson(dealDataJson, "latitude")),
            escapeCSV(getStringFromJson(dealDataJson, "sendStartFreq")),
            escapeCSV(getStringFromJson(dealDataJson, "sendEndFreq")),
            escapeCSV(getStringFromJson(dealDataJson, "accStartFreq")),
            escapeCSV(getStringFromJson(dealDataJson, "accEndFreq")),
            escapeCSV(getStringFromJson(dealDataJson, "maxEmissivePower")),
            escapeCSV(getStringFromJson(dealDataJson, "height")),
            escapeCSV(getStringFromJson(dealDataJson, "deviceFactory")),
            escapeCSV(getStringFromJson(dealDataJson, "deviceModel")),
            escapeCSV(getStringFromJson(dealDataJson, "modelCode")),
            escapeCSV(getStringFromJson(dealDataJson, "antennaModel")),
            escapeCSV(getStringFromJson(dealDataJson, "antennaFactory")),
            escapeCSV(getStringFromJson(dealDataJson, "polarizationMode")),
            escapeCSV(getStringFromJson(dealDataJson, "antennaAzimuth")),
            escapeCSV(getStringFromJson(dealDataJson, "atRang")),
            escapeCSV(getStringFromJson(dealDataJson, "atEang")),
            escapeCSV(getStringFromJson(dealDataJson, "feedLose")),
            escapeCSV(getStringFromJson(dealDataJson, "antGain")),
            escapeCSV(getStringFromJson(dealDataJson, "altitude")),
            formatInteger(getIntegerFromJson(dealDataJson, "setYear")),
            formatInteger(getIntegerFromJson(dealDataJson, "setMonth")),
            escapeCSV(getStringFromJson(dealDataJson, "expandStation")),
            escapeCSV(getStringFromJson(dealDataJson, "attributeStation")),
            escapeCSV(getStringFromJson(dealDataJson, "stServR")),
            escapeCSV(getStringFromJson(dealDataJson, "stScene")),
            formatDate(getDateFromJson(dealDataJson, "trfDate")),
            formatDecimal(getBigDecimalFromJson(dealDataJson, "trfUser")),
            formatDecimal(getBigDecimalFromJson(dealDataJson, "trfData")),
            formatDate(data.getCreatedAt())
        };
        
        writer.write(String.join(",", values));
        writer.newLine();
    }

    /**
     * 转义CSV字段值
     * @param value 字段值
     * @return 转义后的值
     */
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        
        // 如果包含逗号、双引号或换行符，需要用双引号包围
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            // 双引号需要转义为两个双引号
            value = value.replace("\"", "\"\"");
            return "\"" + value + "\"";
        }
        
        return value;
    }

    /**
     * 格式化BigDecimal值
     * @param value BigDecimal值
     * @return 格式化后的字符串
     */
    private String formatDecimal(BigDecimal value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 格式化Integer值
     * @param value Integer值
     * @return 格式化后的字符串
     */
    private String formatInteger(Integer value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 格式化Date值
     * @param date Date值
     * @return 格式化后的字符串
     */
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 撤销任务
     * @param jobId 任务ID
     * @param revokeReason 撤销原因
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> revokeJob(Long jobId, String revokeReason) {
        try {
            // 1. 检查任务是否存在
            TransportJobNew job = transportJobNewService.findById(jobId);
            if (job == null) {
                return this.basicReturnFailure("任务不存在");
            }

            // 2. 检查任务状态是否允许撤销
            if (!isAllowRevoke(job.getStatus())) {
                return this.basicReturnFailure("当前任务状态不允许撤销，状态：" + job.getStatus());
            }

            // 3. 删除相关数据（按照依赖关系倒序删除）
            
            // 3.1 删除正确数据表（这会自动清理对应的分支统计）
            int deletedValidCount = transportRawBtsDealNewService.deleteByJobId(jobId);
            
            // 3.2 删除错误数据日志表（错误数据没有jobBranchId，直接删除即可）
            int deletedInvalidCount = transportRawBtsDealLogNewService.deleteByJobId(jobId);
            
            // 3.3 删除任务分支表（正确数据删除后，分支表也可以删除）
            int deletedBranchCount = transportJobBranchNewService.deleteByJobId(jobId);
            
            // 3.4 更新文件表，清除jobId关联（保留文件记录，但取消与任务的关联）
            int updatedFileCount = transportFileService.clearJobIdByJobId(jobId);

            // 4. 更新任务状态为已撤销
            job.setStatus("revoked");
            job.setUpdatedAt(new Date());
            transportJobNewService.update(job);

            // 5. 记录操作日志
            String logMessage = String.format("任务撤销成功 - 删除正确数据:%d条, 错误数据:%d条, 分支:%d个, 更新文件:%d个", 
                    deletedValidCount, deletedInvalidCount, deletedBranchCount, updatedFileCount);
            if (revokeReason != null && !revokeReason.trim().isEmpty()) {
                logMessage += " - 撤销原因：" + revokeReason;
            }

            // 6. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("jobId", jobId);
            result.put("deletedValidCount", deletedValidCount);
            result.put("deletedInvalidCount", deletedInvalidCount);
            result.put("deletedBranchCount", deletedBranchCount);
            result.put("updatedFileCount", updatedFileCount);
            result.put("revokeReason", revokeReason);
            result.put("message", logMessage);
            
            return this.basicReturnSuccess(result);

        } catch (Exception e) {
            return this.basicReturnFailure("撤销任务失败：" + e.getMessage());
        }
    }

    /**
     * 检查任务状态是否允许撤销
     * @param status 任务状态
     * @return 是否允许撤销
     */
    private boolean isAllowRevoke(String status) {
        // 只有特定状态的任务才能撤销
        List<String> allowedStatuses = Arrays.asList(
            "created",      // 已创建
            "checking",     // 校验中
            "checked",      // 已校验
            "invalid_data", // 数据无效
            "approving"     // 审批中
        );
        return allowedStatuses.contains(status);
    }

    /**
     * 查找历史错误记录（跨所有任务）- 使用业务逻辑唯一键
     * @param entity 完整的数据实体
     * @return 历史错误记录
     */
    private TransportRawBtsDealLogNew findHistoricalErrorRecordByBusinessLogicKey(TransportRawBtsDealNew entity) {
        try {
            // 使用业务逻辑唯一键跨任务查询方法，解决数据修正时的历史错误清理问题
            return transportRawBtsDealLogNewService.findHistoricalErrorByBusinessLogicKey(entity);
        } catch (Exception e) {
            // 如果查询失败，返回null
            return null;
        }
    }

    /**
     * 根据任务ID分页查询错误数据详情（专门查询错误数据）
     * @param vo 查询条件DTO（包含jobId和分页信息）
     * @return 分页查询结果
     */
    public Map<String, Object> getErrorDataDetailByJobId(DataDetailVO vo) {
        try {
            // 从vo中获取jobId并校验
            Long jobId = vo.getJobId();
            if (jobId == null) {
                return this.basicReturnFailure("任务ID不能为空");
            }

            // 检查任务状态
            TransportJobNew job = transportJobNewService.findById(jobId);
            if (job == null) {
                return this.basicReturnFailure("任务不存在");
            }

            // 获取分页参数，设置默认值
            int page = vo.getPage() > 0 ? vo.getPage() : 1;
            int pageSize = vo.getRows() > 0 ? vo.getRows() : 10;
            int offset = (page - 1) * pageSize;

            // 查询总数
            long totalCountLong = transportRawBtsDealLogNewService.countByJobId(jobId);
            int totalCount = (int) totalCountLong;

            // 特殊情况处理：任务状态为invalid_data但没有错误数据时
            if ("invalid_data".equals(job.getStatus()) && totalCount == 0) {
                return this.basicReturnFailure("文件校验失败，请核对表头！");
            }

            // 查询分页数据
            List<TransportRawBtsDealLogNew> errorDataList = transportRawBtsDealLogNewService.findByJobIdWithPaging(jobId, offset, pageSize);
            
            // 转换为DTO格式，包含更详细的错误信息
            List<DataDetailDTO> resultList = new ArrayList<>();
            for (TransportRawBtsDealLogNew errorData : errorDataList) {
                DataDetailDTO dataDto = convertToErrorDataDetailDTO(errorData);
                resultList.add(dataDto);
            }

            // 手动构建分页结果
            Map<String, Object> pageResult = new HashMap<>();
            pageResult.put("rows", resultList);
            pageResult.put("total", totalCount);
            pageResult.put("totalPages", (int) Math.ceil((double) totalCount / pageSize));
            pageResult.put("page", page);
            pageResult.put("pageSize", pageSize);
            pageResult.put("first", page == 1);
            pageResult.put("last", page >= Math.ceil((double) totalCount / pageSize));
            pageResult.put("hasNext", page < Math.ceil((double) totalCount / pageSize));
            pageResult.put("hasPrevious", page > 1);

            return this.basicReturnSuccess(pageResult);

        } catch (Exception e) {
            return this.basicReturnFailure("查询错误数据详情失败：" + e.getMessage());
        }
    }

    /**
     * 将TransportRawBtsDealLogNew转换为增强的DataDetailDTO（包含错误字段信息）
     * @param errorData 错误数据实体
     * @return DTO对象
     */
    private DataDetailDTO convertToErrorDataDetailDTO(TransportRawBtsDealLogNew errorData) {
        DataDetailDTO dto = new DataDetailDTO();
        dto.setDataType("error");
        dto.setErrorMessage(errorData.getErrorMessage());
        dto.setErrorType(errorData.getErrorType());
        dto.setFieldName(errorData.getFieldName());
        dto.setOriginalValue(errorData.getOriginalValue());

        // 解析deal_data JSON字符串
        if (StringUtils.isNotBlank(errorData.getDealData())) {
            try {
                JSONObject dealDataJson = JSONObject.parseObject(errorData.getDealData());
                
                // 从JSON中提取基站相关业务数据
                dto.setCellName(getStringFromJson(dealDataJson, "cellName"));
                dto.setCellId(getStringFromJson(dealDataJson, "cellId"));
                dto.setBtsName(getStringFromJson(dealDataJson, "btsName"));
                dto.setBtsId(getStringFromJson(dealDataJson, "btsId"));
                dto.setTechType(getStringFromJson(dealDataJson, "techType"));
                dto.setLocation(getStringFromJson(dealDataJson, "location"));
                dto.setCounty(getStringFromJson(dealDataJson, "county"));
                dto.setLongitude(getStringFromJson(dealDataJson, "longitude"));
                dto.setLatitude(getStringFromJson(dealDataJson, "latitude"));
                dto.setSendStartFreq(getStringFromJson(dealDataJson, "sendStartFreq"));
                dto.setSendEndFreq(getStringFromJson(dealDataJson, "sendEndFreq"));
                dto.setAccStartFreq(getStringFromJson(dealDataJson, "accStartFreq"));
                dto.setAccEndFreq(getStringFromJson(dealDataJson, "accEndFreq"));
                dto.setMaxEmissivePower(getStringFromJson(dealDataJson, "maxEmissivePower"));
                dto.setHeight(getStringFromJson(dealDataJson, "height"));
                dto.setDeviceFactory(getStringFromJson(dealDataJson, "deviceFactory"));
                dto.setDeviceModel(getStringFromJson(dealDataJson, "deviceModel"));
                dto.setModelCode(getStringFromJson(dealDataJson, "modelCode"));
                dto.setAntennaModel(getStringFromJson(dealDataJson, "antennaModel"));
                dto.setAntennaFactory(getStringFromJson(dealDataJson, "antennaFactory"));
                dto.setPolarizationMode(getStringFromJson(dealDataJson, "polarizationMode"));
                dto.setAntennaAzimuth(getStringFromJson(dealDataJson, "antennaAzimuth"));
                dto.setAtRang(getStringFromJson(dealDataJson, "atRang"));
                dto.setAtEang(getStringFromJson(dealDataJson, "atEang"));
                dto.setFeederLoss(getStringFromJson(dealDataJson, "feedLose"));
                dto.setAntennaGain(getStringFromJson(dealDataJson, "antGain"));
                dto.setAltitude(getStringFromJson(dealDataJson, "altitude"));
                dto.setSetYear(getStringFromJson(dealDataJson, "setYear"));
                dto.setSetMonth(getStringFromJson(dealDataJson, "setMonth"));
                dto.setExpandStation(getStringFromJson(dealDataJson, "expandStation"));
                dto.setAttributeStation(getStringFromJson(dealDataJson, "attributeStation"));
                dto.setStServR(getStringFromJson(dealDataJson, "stServR"));
                dto.setTrfDate(getDateFromJson(dealDataJson, "trfDate"));
                BigDecimal trfUserBd = getBigDecimalFromJson(dealDataJson, "trfUser");
                dto.setTrfUser(trfUserBd != null ? trfUserBd.doubleValue() : null);
                BigDecimal trfDataBd = getBigDecimalFromJson(dealDataJson, "trfData");
                dto.setTrfData(trfDataBd != null ? trfDataBd.doubleValue() : null);
                dto.setStScene(getStringFromJson(dealDataJson, "stScene"));

            } catch (Exception e) {
                // JSON解析失败时设置默认值
                System.err.println("解析deal_data JSON失败：" + e.getMessage());
            }
        }

        dto.setCreateTime(errorData.getCreatedAt());
        return dto;
    }

    /**
     * 暂存任务（只创建任务和关联文件，不进行数据校验）
     * 
     * 业务规则：
     * 1. 有uploadToken没jobId：创建任务并绑定文件
     * 2. 没uploadToken没jobId：单纯创建任务
     * 4. 有jobId没uploadToken：编辑任务信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> draftJob(SubmitJobVO submitJobVO, String token) {
        try {
            // 通过token获取用户信息
            UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
            if (usersDTO == null) {
                return this.basicReturnFailure(401);
            }

            String createdBy = usersDTO.getName(); // 设置上传人姓名
            String operatorCode=usersDTO.getType();
            Long userId = Long.valueOf(usersDTO.getId()); // 设置用户ID

            // 从VO对象中获取参数
            String uploadToken = submitJobVO.getUploadToken();
            String applyType = submitJobVO.getApplyType();
            Long jobId = submitJobVO.getJobId();
            String jobName = submitJobVO.getJobName();

            TransportJobNew job;
            List<TransportFile> files = new ArrayList<>();
            boolean isNewJob = false; // 标记是否为新创建的任务

            // 根据不同的参数组合执行不同的逻辑
            if (jobId == null) {
                // 场景1&2：创建新的暂存任务
                isNewJob = true;
                if (StringUtils.isNotBlank(uploadToken)) {
                    // 场景1：有uploadToken没jobId - 创建任务并绑定文件
                    files = transportFileService.findByUploadToken(uploadToken);
                    if (files.isEmpty()) {
                        return this.basicReturnFailure("未找到与uploadToken关联的文件");
                    }
                } 
                // 场景2：没uploadToken没jobId - 单纯创建任务（files为空列表）

                // 创建新的暂存任务
                job = new TransportJobNew();
                job.setOperatorCode(operatorCode);
                job.setStatus("created"); // 设置为暂存状态
                job.setApplyType(applyType);
                job.setCreatedBy(createdBy);
                job.setUserId(userId);
                job.setJobName(jobName);
                job.setCreatedAt(new Date());
                job.setUpdatedAt(new Date());

                // 保存任务并获取ID
                Object savedId = transportJobNewService.save(job);
                jobId = extractJobId(savedId, job);
                if (jobId == null) {
                    return this.basicReturnFailure("暂存任务创建失败，无法获取任务ID");
                }

                // 如果有uploadToken，将文件绑定到新任务
                if (StringUtils.isNotBlank(uploadToken)) {
                    transportFileService.updateJobIdAndClearToken(jobId, uploadToken);
                }

            } else {
                // 场景3&4：操作现有任务
                job = transportJobNewService.findById(jobId);
                if (job == null) {
                    return this.basicReturnFailure("任务不存在");
                }

                // 检查任务是否为暂存状态，只有暂存状态的任务可以编辑
                if (!"created".equals(job.getStatus())) {
                    return this.basicReturnFailure("只能编辑暂存状态的任务，当前状态：" + job.getStatus());
                }

             /*   if (StringUtils.isNotBlank(uploadToken)) {
                    // 场景3：有jobId有uploadToken - 关联文件到现有任务
                    List<TransportFile> newFiles = transportFileService.findByUploadToken(uploadToken);
                    if (newFiles.isEmpty()) {
                        return this.basicReturnFailure("未找到与uploadToken关联的文件");
                    }
                    // 将新文件绑定到现有任务
                    transportFileService.updateJobIdAndClearToken(jobId, uploadToken);
                }*/

                // 场景4：有jobId没uploadToken - 编辑任务信息（不处理文件）

                // 获取任务当前关联的文件
                files = transportFileService.findByJobId(jobId);

                // 更新任务信息（保持暂存状态）
                job.setUpdatedAt(new Date());
                if (jobName != null) {
                    job.setJobName(jobName);
                }
                if (applyType != null) {
                    job.setApplyType(applyType);
                }
                transportJobNewService.update(job);
            }

            // 获取最终的文件列表（可能在上面的操作中发生了变化）
            List<TransportFile> finalFiles = transportFileService.findByJobId(jobId);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("jobId", String.valueOf(jobId));
            result.put("status", job.getStatus());
            result.put("draftTime", new Date());
            result.put("fileCount", finalFiles.size());
            
            // 根据操作类型设置不同的成功消息
            String successMessage;
            if (isNewJob) { // 新创建的任务
                if (!finalFiles.isEmpty()) {
                    successMessage = "暂存任务创建成功，已绑定 " + finalFiles.size() + " 个文件";
                } else {
                    successMessage = "暂存任务创建成功";
                }
            } else { // 编辑现有任务
                if (StringUtils.isNotBlank(uploadToken)) {
                    successMessage = "文件关联成功，当前任务共有 " + finalFiles.size() + " 个文件";
                } else {
                    successMessage = "暂存任务更新成功";
                }
            }
            result.put("message", successMessage);
            
            return this.basicReturnSuccess(result);

        } catch (Exception e) {
            return this.basicReturnFailure("暂存任务失败：" + e.getMessage());
        }
    }

    /**
     * 查询暂存任务列表
     * @param dto 查询条件
     * @param token 用户token
     * @return 暂存任务列表
     */
    public Map<String, Object> findDraftJobs(TransportJobNewSearchVO dto, String token) {
        try {
            // 根据token获取用户信息
            UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
            if (usersDTO == null) {
                return this.basicReturnFailure("用户未登录或token无效");
            }

            // 设置查询条件为暂存状态
            dto.setStatus("created");
            
            // 设置权限限制：用户只能查看自己提交的暂存任务，但admin用户可以查看所有暂存任务
            if (!"admin".equals(usersDTO.getName())) {
                dto.setCreatedBy(usersDTO.getName());
            }
            // 如果是admin用户，不设置createdBy限制，可以查看所有暂存任务
            
            // 执行查询
            List<TransportJobNewSearchVO> jobs = transportJobNewService.findPageWithConditions(dto);
            
            // 为每个任务查询关联的文件数量
            for (TransportJobNewSearchVO job : jobs) {
                List<TransportFile> files = transportFileService.findByJobId(job.getId());
                job.setFileCount(files.size());
            }
            
            return this.basicReturnSuccess(jobs);
            
        } catch (Exception e) {
            return this.basicReturnFailure("查询暂存任务失败：" + e.getMessage());
        }
    }

    /**
     * 删除暂存任务
     * @param jobId 任务ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> deleteDraftJob(Long jobId) {
        try {
            TransportJobNew job = transportJobNewService.findById(jobId);
            if (job == null) {
                return this.basicReturnFailure("任务不存在");
            }

            // 检查任务状态，只有暂存状态才能删除
            if (!"created".equals(job.getStatus())) {
                return this.basicReturnFailure("只能删除暂存状态的任务，当前状态：" + job.getStatus());
            }

            // 删除关联的文件记录（软删除）
            List<TransportFile> files = transportFileService.findByJobId(jobId);
            for (TransportFile file : files) {
                transportFileService.softDeleteFile(file.getId());
            }

            // 删除任务记录
            int result = transportJobNewService.delete(jobId);
            if (result > 0) {
                return this.basicReturnSuccess("暂存任务删除成功");
            } else {
                return this.basicReturnFailure("暂存任务删除失败");
            }

        } catch (Exception e) {
            return this.basicReturnFailure("删除暂存任务失败：" + e.getMessage());
        }
    }

    /**
     * 根据Guid修改流程状态
     */
    public int updateStatusByGuid(String status, Long guid) {
        TransportJobNew transportJobNew = new TransportJobNew();
        transportJobNew.setId(guid);
        transportJobNew.setStatus(status);
        transportJobNew.setUpdatedAt(new Date());
        return transportJobNewService.update(transportJobNew);
    }

    /**
     * 修改
     */
    public int update(TransportJobNew transportJobNew) {
        return transportJobNewService.update(transportJobNew);
    }

    public TransportJobNew findById(Long id){
        return transportJobNewService.findById(id);
    }

    public Map<String, Object> findAllPage(String token, TransportJobNewSearchVO dto) {
        //UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        Long userId = Long.valueOf(4); // 设置用户ID

        List<TransportJobNewSearchVO> list = transportJobNewService.findAllPage(dto,userId);
        return this.basicReturnResultJson(new PageHandle(dto).buildPage(list));
    }

    public Map<String, Object> detail(Long jobId) {
        try{

            List<ApprovalTransportJob> list = transportJobNewService.findOneByGuid(jobId);
            if (list == null) {
                return this.basicReturnFailure("任务不存在");
            }

            return this.basicReturnSuccess(list);

        }catch (Exception e){
            return this.basicReturnFailure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 转换任务实体为任务信息DTO（ID字段转为String）
     * @param job 任务实体
     * @return 任务信息DTO
     */
    private TransportJobNewDTO convertToJobInfoDTO(TransportJobNew job) {
        TransportJobNewDTO jobInfoDTO = new TransportJobNewDTO();

        BeanUtils.copyProperties(job,jobInfoDTO);
        
        // 将Long类型的ID字段转换为String
        jobInfoDTO.setId(job.getId() != null ? job.getId().toString() : null);
        jobInfoDTO.setUserId(job.getUserId() != null ? job.getUserId().toString() : null);

        
        return jobInfoDTO;
    }

    /**
     * 转换文件实体列表为文件信息DTO列表（ID字段转为String）
     * @param files 文件实体列表
     * @return 文件信息DTO列表
     */
    private List<FileInfoDTO> convertToFileInfoDTOs(List<TransportFile> files) {
        List<FileInfoDTO> fileInfoDTOs = new ArrayList<>();
        
        for (TransportFile file : files) {
            FileInfoDTO fileInfoDTO = new FileInfoDTO();

            BeanUtils.copyProperties(file,fileInfoDTO);
            
            // 将Long类型的ID字段转换为String
            fileInfoDTO.setId(file.getId() != null ? file.getId().toString() : null);
            fileInfoDTO.setJobId(file.getJobId() != null ? file.getJobId().toString() : null);
            

            fileInfoDTOs.add(fileInfoDTO);
        }
        
        return fileInfoDTOs;
    }

    /**
     * 根据regionCode获取对应的regionName
     * @param regionCode 地区代码
     * @param orgRegionList 地区列表
     * @return 地区名称
     */
    private String getRegionNameByCode(String regionCode, List<RegionDTO> orgRegionList) {
        if (regionCode == null || orgRegionList == null) {
            return null;
        }
        
        for (RegionDTO regionDTO : orgRegionList) {
            if (regionCode.equals(regionDTO.getCode())) {
                return regionDTO.getName();
            }
        }
        
        return null;
    }

    /**
     * 根据任务ID获取数据统计信息
     * @param jobId 任务ID
     * @return 数据统计DTO
     */
    private DataStatsDTO getDataStatsByJobId(Long jobId) {
        DataStatsDTO dataStatsDTO = new DataStatsDTO();
        
        try {
            // 统计正确数据数量
            long validCount = transportRawBtsDealNewService.countByJobId(jobId);
            
            // 统计错误数据数量
            long invalidCount = transportRawBtsDealLogNewService.countByJobId(jobId);
            
            // 计算总数
            long totalCount = validCount + invalidCount;
            
            dataStatsDTO.setValidCount(validCount);
            dataStatsDTO.setInvalidCount(invalidCount);
            dataStatsDTO.setTotalCount(totalCount);
            
        } catch (Exception e) {
            // 如果统计失败，设置为0
            dataStatsDTO.setValidCount(0);
            dataStatsDTO.setInvalidCount(0);
            dataStatsDTO.setTotalCount(0);
        }
        
        return dataStatsDTO;
    }


    /**
     * 审核任务后判断后续任务处理
     */
    public void dealCheckedData(String approvalId,UsersDTO usersDTO){
        ApprovalTransportJob approvalTransportJob = approvalTransportJobWebService.findById(approvalId);
        if (approvalTransportJob!= null){
            //判断TransportJob任务下当前地区的approvalTransportJob是否全部审核完成
            List<ApprovalTransportJob> regionCodeByStatus = approvalTransportJobWebService.findRegionCodeByStatusJobId(Arrays.asList("revoked", "done"), usersDTO,
                    approvalTransportJob.getJobId(), approvalTransportJob.getRegionCode());
            if (regionCodeByStatus.isEmpty()){
                //寻找当前TransportJob运营商上传的下一批任务
                TransportJobNew lastTransportJob = transportJobNewService.findLastTransportJob(approvalTransportJob.getJobId(), approvalTransportJob.getOrgType());
                if (lastTransportJob!= null){
                    List<TransportSchedule> transportScheduleList = transportScheduleService.findByJobIdAndRegionCode(approvalTransportJob.getJobId(), approvalTransportJob.getRegionCode());
                    if (!transportScheduleList.isEmpty()){
                        if ("1".equals(lastTransportJob.getApplyType())){
                            //开启线程进行新增/变更比对处理
                            CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation1(String.valueOf(lastTransportJob.getId()),null,transportScheduleList,usersDTO), asyncExecutor);
                        }else if ("2".equals(lastTransportJob.getApplyType()) || "3".equals(lastTransportJob.getApplyType())){
                            //开启线程进行注销处理
                            CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation2(String.valueOf(lastTransportJob.getId()),null,transportScheduleList,usersDTO), asyncExecutor);
                        }
                        //删除transportSchedule表数据
//                        transportScheduleService.deleteByJobIdAndRegionCode(approvalTransportJob.getJobId(), approvalTransportJob.getRegionCode());
                    }
                }
            }
        }
    }

    /**
     * 查询任务处理状态和进度
     * @param jobId 任务ID
     * @return 处理状态信息
     */
    public Map<String, Object> getJobProcessingStatus(Long jobId) {
        try {
            TransportJobNew job = transportJobNewService.findById(jobId);
            if (job == null) {
                return this.basicReturnFailure("任务不存在");
            }

            Map<String, Object> statusInfo = new HashMap<>();
            statusInfo.put("jobId", jobId);
            statusInfo.put("status", job.getStatus());
            statusInfo.put("updatedAt", job.getUpdatedAt());

            // 根据状态返回不同的信息
            switch (job.getStatus()) {
                case "checking":
                    statusInfo.put("message", "数据校验中，请稍候...");
                    statusInfo.put("isProcessing", true);
                    
                    // 获取文件处理进度（只统计CSV文件）
                    List<TransportFile> files = transportFileService.findByJobId(jobId);
                    int totalFiles = files.size();
                    int totalCsvFiles = 0;
                    int processedCsvFiles = 0;
                    int skippedFiles = 0;
                    
                    for (TransportFile file : files) {
                        if ("1".equals(file.getFileType())) { // CSV文件
                            totalCsvFiles++;
                            if (file.getStatus() != null && file.getStatus() != 1) { // 1表示未处理
                                processedCsvFiles++;
                            }
                        } else {
                            skippedFiles++; // 非CSV文件被跳过
                        }
                    }
                    
                    statusInfo.put("totalFiles", totalFiles);
                    statusInfo.put("totalCsvFiles", totalCsvFiles);
                    statusInfo.put("processedCsvFiles", processedCsvFiles);
                    statusInfo.put("skippedFiles", skippedFiles);
                    
                    // 进度基于CSV文件计算
                    double progress = totalCsvFiles > 0 ? (processedCsvFiles * 100.0 / totalCsvFiles) : 0;
                    statusInfo.put("progress", progress);
                    
                    // 更详细的消息
                    if (totalCsvFiles > 0) {
                        statusInfo.put("message", String.format("数据校验中，请稍候... (处理进度: %d/%d CSV文件, %.1f%%)", 
                                processedCsvFiles, totalCsvFiles, progress));
                    } else {
                        statusInfo.put("message", "未找到需要处理的CSV数据文件");
                    }
                    break;
                    
                case "checked":
                    statusInfo.put("message", "数据校验完成，所有数据均通过校验");
                    statusInfo.put("isProcessing", false);
                    
                    // 获取数据统计
                    DataStatsDTO dataStats = getDataStatsByJobId(jobId);
                    statusInfo.put("dataStats", dataStats);
                    break;
                    
                case "invalid_data":
                    statusInfo.put("message", "数据校验完成，发现错误数据");
                    statusInfo.put("isProcessing", false);
                    
                    // 获取数据统计
                    DataStatsDTO dataStatsInvalid = getDataStatsByJobId(jobId);
                    statusInfo.put("dataStats", dataStatsInvalid);
                    break;
                    
                case "submitted":
                    statusInfo.put("message", "任务已提交");
                    statusInfo.put("isProcessing", false);
                    break;
                    
                default:
                    statusInfo.put("message", "状态：" + job.getStatus());
                    statusInfo.put("isProcessing", false);
                    break;
            }

            return this.basicReturnSuccess(statusInfo);

        } catch (Exception e) {
            return this.basicReturnFailure("查询处理状态失败：" + e.getMessage());
        }
    }
}
