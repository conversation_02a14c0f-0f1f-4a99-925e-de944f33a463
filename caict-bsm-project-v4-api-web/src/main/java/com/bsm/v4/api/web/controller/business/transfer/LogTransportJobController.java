package com.bsm.v4.api.web.controller.business.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.LogTransportJobWebService;
import com.bsm.v4.system.model.dto.business.transfer.LogTransportJobDTO;
import com.bsm.v4.system.model.vo.business.transfer.LogTransportJobVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created by dengsy on 2020-04-22.
 */
/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@RestController
@RequestMapping(value = "/apiWeb/transfer/logTransportJob")
@Api(value = "web端异常数据接口", tags = "web端异常数据接口")
public class LogTransportJobController extends BasicController {

    @Autowired
    LogTransportJobWebService service;

    @ApiOperation(value = "查询csv文件日志详情", notes = "查询csv文件日志详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileGuid", value = "文件guid", required = true, paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/findAllByLogTypeFileId")
    public JSONObject findAllByLogTypeFileId(@RequestBody LogTransportJobDTO logTransportJobDTO) {
        return this.basicReturnJson(logTransportJobDTO, LogTransportJobWebService.class, (model, service) -> service.findAllByLogTypeFileId(logTransportJobDTO));
    }

    @ApiOperation(value = "下载csv文件日志详情", notes = "下载csv文件日志详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileGuid", value = "文件guid", required = true, paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/exportLog")
    public JSONObject exportLog(@RequestBody LogTransportJobDTO logTransportJobDTO) {
        return service.exportLog(logTransportJobDTO);
    }

    /**
     * 下载校验后错误的日志
     *
     * @param logTransportJobDTO
     * @return
     */
    @ApiOperation(value = "下载文件校验错误详情", notes = "下载文件校验错误详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileGuid", value = "文件guid", required = true, paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/exportErrorLog")
    public JSONObject exportErrorLog(@RequestBody LogTransportJobDTO logTransportJobDTO, HttpServletResponse response) {
        return service.exportErrorLog(logTransportJobDTO, response);
    }

    @ApiOperation(value = "初始加载登录用户待办任务", notes = "初始加载登录用户待办任务")
    @PostMapping(value = "/loadInitTaskDetail")
    public JSONObject loadInitTaskDetail(@RequestHeader("token")String token) {
        return this.basicReturnJson(token, LogTransportJobWebService.class, (string, service) -> service.loadInitTaskDetail(token));
    }

    @ApiOperation(value = "待办数据详情导出", notes = "待办数据详情导出")
    @PostMapping(value = "/exportTaskDetail")
    public JSONObject exportTaskDetail(@RequestBody LogTransportJobVO logTransportJobVO,HttpServletRequest request, HttpServletResponse response) {
        Object accessToken = request.getHeader("token");
        logTransportJobVO.setToken(accessToken.toString());
        return this.basicReturnJson(logTransportJobVO, LogTransportJobWebService.class, (vo, service) -> service.exportTaskDetail(logTransportJobVO,response));
    }
}
