package com.bsm.v4.api.web.controller.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.security.DictionaryWebService;
import com.bsm.v4.system.model.vo.security.DictionarySearchVO;
import com.bsm.v4.system.model.vo.security.DictionaryVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Title: DictionaryController
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.controller.security
 * @Date 2023/8/16 15:55
 * @description:
 */
@RestController
@RequestMapping(value = "/apiWeb/security/dictionary")
@Api(value = "web端-系统-数据字典接口", tags = "web端-系统-数据字典接口")
public class DictionaryController extends BasicController {

    @ApiOperation(value = "添加、修改数据字典", notes = "添加、修改数据字典接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dictionaryVO", value = "数据字典对象", required = true, paramType = "body", dataType = "dictionaryVO")
    })
    @PostMapping(value = "/")
    public JSONObject save(@RequestBody DictionaryVO dictionaryVO) {
        return this.basicReturnJson(dictionaryVO, DictionaryWebService.class, (vo, service) -> service.save(vo));
    }

    @ApiOperation(value = "批量添加数据字典", notes = "批量添加接口数据字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dictionaryVOList", value = "数据字典对象集合", allowMultiple = true, required = true, paramType = "body", dataType = "dictionaryVO")
    })
    @PostMapping(value = "/insertBatch")
    public JSONObject insertBatch(@RequestBody List<DictionaryVO> dictionaryVOList) {
        return this.basicReturnJson(dictionaryVOList, DictionaryWebService.class, (vo, service) -> service.insertBatch(vo));
    }

    @ApiOperation(value = "批量删除数据字典", notes = "批量删除数据字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键集合", required = true, paramType = "body", allowMultiple = true, dataType = "String")
    })
    @PostMapping(value = "/deleteBatch")
    public JSONObject deleteBatch(@RequestBody String[] ids) {
        return this.basicReturnJson(ids, DictionaryWebService.class, (vo, service) -> service.deleteBatch(vo));
    }

    @ApiOperation(value = "查询全部数据字典", notes = "查询全部数据字典接口")
    @GetMapping(value = "/")
    public JSONObject findAll() {
        return this.basicReturnJson(DictionaryWebService.class, (service) -> service.findAll());
    }

    @ApiOperation(value = "分页查询", notes = "分页查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dictionarySearchVO", value = "数据字典搜索对象", required = true, paramType = "body", dataType = "dictionarySearchVO")
    })
    @PostMapping(value = "/findAllPageByWhere")
    public JSONObject findAllPage(@RequestBody DictionarySearchVO dictionarySearchVO) {
        return this.basicReturnJson(dictionarySearchVO, DictionaryWebService.class, (vo, service) -> service.findAllPageByWhere(vo));
    }

    @ApiOperation(value = "查询全部数据字典(条件查询)", notes = "查询全部数据字典(条件查询)接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dictionarySearchVO", value = "数据字典搜索对象", required = true, paramType = "body", dataType = "dictionarySearchVO")
    })
    @PostMapping(value = "/findAllWhere")
    public JSONObject findAllWhere(@RequestBody DictionarySearchVO dictionarySearchVO) {
        return this.basicReturnJson(dictionarySearchVO, DictionaryWebService.class, (vo, service) -> service.findAllWhere(vo));
    }

    @ApiOperation(value = "查询所有数据字典（树形结构）(条件查询)", notes = "查询所有数据字典（树形结构）(条件查询)接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dictionarySearchVO", value = "数据字典搜索对象", required = true, paramType = "body", dataType = "dictionarySearchVO")
    })
        @PostMapping(value = "/findAllTreeWhere")
    public JSONObject findAllTreeWhere(@RequestBody DictionarySearchVO dictionarySearchVO) {
        return this.basicReturnJson(dictionarySearchVO, DictionaryWebService.class, (vo, service) -> service.findAllTreeWhereNew(vo));
    }

    @ApiOperation(value = "查询一个数据字典信息", notes = "查询一个数据字典信息接口")
    @ApiImplicitParam(name = "id", value = "数据字典id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findOne/{id}")
    public JSONObject findOne(@PathVariable("id") String id) {
        return this.basicReturnJson(id, DictionaryWebService.class, (vo, service) -> service.findOneById(vo));
    }

    @ApiOperation(value = "查询所有数据字典（根据parentId)", notes = "查询所有数据字典（根据parentId)接口")
    @ApiImplicitParam(name = "parentId", value = "数据字父级id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findAllByParentId/{parentId}")
    public JSONObject findAllByParentId(@PathVariable String parentId) {
        return this.basicReturnJson(parentId, DictionaryWebService.class, (vo, service) -> service.findAllByParentId(vo));
    }

    @ApiOperation(value = "查询所有数据字典（根据parentID)（树形结构）", notes = "查询所有数据字典（根据parentID)（树形结构）接口")
    @ApiImplicitParam(name = "parentId", value = "数据字父级id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findTreeByParentId/{parentId}")
    public JSONObject findTreeByParentId(@PathVariable String parentId) {
        return this.basicReturnJson(parentId, DictionaryWebService.class, (vo, service) -> service.findTreeByParentId(vo));
    }

    @ApiOperation(value = "删除数据字典", notes = "删除数据字典接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据字典id", required = true, dataType = "String")
    })
    @DeleteMapping(value = "/delete/{id}")
    public JSONObject delete(@PathVariable String id) {
        return this.basicReturnJson(id, DictionaryWebService.class, (vo, service) -> service.delete(id));
    }

    @ApiOperation(value = "根据父级编号查询", notes = "根据父级编号查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编号", required = true, dataType = "String")
    })
    @GetMapping(value = "/findAllByParentCode/{code}")
    public JSONObject findAllByParentCode(@PathVariable("code") String code) {
        return this.basicReturnJson(code, DictionaryWebService.class, (vo, service) -> service.findAllByParentCode(vo));

    }

    @ApiOperation(value = "修改数据字典状态", notes = "修改数据字典状态接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据字典id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "数据字典状态", required = true, dataType = "int")
    })
    @PutMapping(value = "/updateStatusById/{id}/{status}")
    public JSONObject updateStatusById(@PathVariable("id") String id, @PathVariable("status") int status) {
        return this.basicReturnJson(id, DictionaryWebService.class, (vo, service) -> service.updateStatusById(id, status));
    }


}
