package com.bsm.v4.api.web.controller.document;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.document.HdfsWebService;
import com.bsm.v4.system.model.vo.business.transfer.TransportFileVO;
import com.bsm.v4.system.model.vo.business.transferNew.FileToUploadVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: ycp
 * @createTime: 2023/09/04 15:19
 * @company: 成渝（成都）信息通信研究院
 * @description: hadoop文件上传controller
 */
@RestController
@RequestMapping(value = "/apiWeb/document/hdfs")
@Api(value = "web端-文件系统-hdfs文件处理接口", tags = "web端-文件系统-hdfs文件处理接口")
public class HdfsController extends BasicController {

    @Autowired
    private HdfsWebService hdfsWebService;

    @ApiOperation(value = "上传hdfs文件", notes = "上传文件接口")
    @PostMapping(value = "/hdfsUpload")
    public JSONObject hdfsUpload(@RequestParam("file") MultipartFile file, @RequestParam String fileType,@RequestHeader("token") String token) {
        return this.basicReturnJson(file, HdfsWebService.class, (vo, service) -> service.hdfsUpload(vo, fileType,token));
    }

    @ApiOperation(value = "上传hdfs文件（校验）", notes = "支持完整的文件上传功能，包括任务关联、用户信息等")
    @PostMapping(value = "/hdfsUploadEnhanced")
    public JSONObject hdfsUploadEnhanced(
            @ApiParam(value = "上传的文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "文件类型", required = true) @RequestParam String fileType,
            @ApiParam(value = "文件上传参数") @ModelAttribute FileToUploadVO fileToUploadVO,
            @ApiParam(value = "用户token") @RequestHeader(value = "token", required = false) String token) {

        return this.basicReturnJson(file, HdfsWebService.class,
                (fileParam, service) -> service.hdfsUpload(fileParam, fileType, fileToUploadVO, token));
    }

    @ApiOperation(value = "删除hdfs文件", notes = "删除文件接口")
    @DeleteMapping(value = "/delete/{fileId}")
    public JSONObject delete(@PathVariable String fileId) {
        return this.basicReturnJson(fileId, HdfsWebService.class, (vo, service) -> service.deleteFile(vo));
    }

    @ApiOperation(value = "下载hdfs文件", notes = "下载文件接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fastFdsVO", value = "FastFDS文件处理对象", required = true, paramType = "body", dataType = "fastFdsVO")
    })
    @GetMapping(value = "/download/{fileId}")
    public JSONObject download(@PathVariable String fileId) {
        return this.basicReturnJson(fileId, HdfsWebService.class, (vo, service) -> service.downloadFile(vo));
    }

    @ApiOperation(value = "获取hdfs文件流测试", notes = "获取hdfs文件流测试")
    @GetMapping(value = "/getInput/{fileId}")
    public JSONObject getInput(@PathVariable String fileId) {
       return this.basicReturnJson(fileId, HdfsWebService.class, (vo, service) -> service.getFileInputStream(vo));
    }

    @ApiOperation(value = "以流方式下载hdfs文件", notes = "以流方式下载hdfs文件接口")
    @PostMapping(value = "/downloadStream")
    public void downloadStream(@RequestBody TransportFileVO fileVO, HttpServletResponse response) {
        hdfsWebService.downloadStream(fileVO, response);
    }
}
