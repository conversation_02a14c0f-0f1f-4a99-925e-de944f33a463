package com.bsm.v4.api.web.controller.business.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.ApprovalTransportJobWebService;
import com.bsm.v4.api.web.service.bussiness.transfer_in.ApplyJobInWebService;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.bsm.v4.system.model.vo.business.transfer.ApprovalScheduleVO;
import com.bsm.v4.system.model.vo.business.transfer.ApprovalTransportJobVO;
import com.bsm.v4.system.model.vo.business.transferNew.TransportNewJobVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created by dengsy on 2020-05-08.
 */
@RestController
@RequestMapping(value = "/apiWeb/transfer/approvalTransportJob")
@Api(tags = "web端审核任务管理接口")
public class ApprovalTransportJobController extends BasicController {

    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;
    @Autowired
    private ApplyJobInWebService applyJobInWebService;

    @ApiOperation(value = "创建任务", notes = "创建任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/createApproveJob")
    public JSONObject createApproveJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.createApproveJob(dto));
    }

//    @ApiOperation(value = "文件和appJob的关联", notes = "文件和appJob的关联接口")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
//    })
//    @PostMapping(value = "/setFileJobId")
//    public void setFileJobId(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
//        Object accessToken = request.getHeader("token");
//        dto.setToken(accessToken.toString());
//        approvalTransportJobWebService.setFileJobId(dto);
//    }

    @ApiOperation(value = "校验处理上传文件", notes = "校验处理上传文件接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/processingApproveJob")
    public JSONObject processingApproveJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.processingApproveJob(dto));

    }

    @ApiOperation(value = "重点台站校验", notes = "重点台站校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/commitCoordinate")
    public JSONObject commitCoordinate(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.commitCoordinate(dto));

    }

    @ApiOperation(value = "处理重点台站上传文件", notes = "处理重点台站上传文件接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/processingCoordinate")
    public JSONObject processingCoordinate(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.processingCoordinate(dto));
    }

    @ApiOperation(value = "重点台站不同意提交", notes = "重点台站不同意提交接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appGuid", value = "任务id", paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/commitApproveJobCoordinate/{appGuid}", method = RequestMethod.GET)
    public JSONObject commitApproveJobCoordinate(@PathVariable("appGuid") String appGuid) {
        return this.basicReturnJson(appGuid, ApprovalTransportJobWebService.class, (map, service) -> approvalTransportJobWebService.commitApproveJobCoordinate(appGuid));
    }

    @ApiOperation(value = "提交任务", notes = "提交任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/commitApproveJob")
    public JSONObject commitApproveJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.commitApproveJob(dto));
    }

    @ApiOperation(value = "审核任务", notes = "审核任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/approveApproveJob")
    public JSONObject approveApproveJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        //Object accessToken = request.getHeader("token");
        //dto.setToken("12345678910");
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.approveApproveJob(dto,request));
    }

    @ApiOperation(value = "分页查看对比任务列表", notes = "分页查看对比任务列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/findPageCompareJobVOListByPage")
    public JSONObject findTransportJobVOLogPage(@RequestBody ApprovalTransportJobDTO dto) {
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findPageCompareJobVOListByPage(dto));
    }

    @ApiOperation(value = "分页查看异常任务列表", notes = "分页查看异常任务列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/findApprovalTransportJobVOLogPage")
    public JSONObject findApprovalTransportJobVOLogPage(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findApprovalTransportJobVOLogPage(dto));
    }

    @ApiOperation(value = "分页查询待办任务", notes = "分页查询待办任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/findAllPageByWhere")
    public JSONObject findAllPageByWhere(@RequestBody ApprovalTransportJobDTO dto) {
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findAllPageByWhere(dto));
    }

    @ApiOperation(value = "分页查看审核列表历史记录（无委）", notes = "分页查看审核列表历史记录（无委）接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/findAllPageAppTransportJob")
    public JSONObject findAllPageAppTransportJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findAllPageAppTransportJobByJur(dto));
    }

    @ApiOperation(value = "查询详情", notes = "查询详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appGuid", value = "审核任务guid", paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/approveDetail/{appGuid}", method = RequestMethod.GET)
    public JSONObject approveDetail(@PathVariable("appGuid") String appGuid) {
        return this.basicReturnJson(appGuid, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.approveDetail(appGuid));
    }

    @ApiOperation(value = "查询审核任务", notes = "查询审核任务接口")
    @PostMapping(value = "/findApprovalTransportJobDTOPage")
    public JSONObject findApprovalTransportJobDTOPage(@RequestBody TransportNewJobVO JobSearchVO) {
        return this.basicReturnJson(JobSearchVO, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findApprovalTransportJobDTOPage(JobSearchVO));
    }

    @ApiOperation(value = "办件查询审核记录表", notes = "办件查询审核记录表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobVO", value = "approvalTransportJobVO", required = true, paramType = "body", dataType = "approvalTransportJobVO")
    })
    @PostMapping(value = "/findApprovalScheduleDTOPage")
    public JSONObject findApprovalScheduleDTOPage(@RequestBody ApprovalTransportJobVO jobVO) {
        return this.basicReturnJson(jobVO, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findApprovalScheduleDTOPage(jobVO));
    }

    @ApiOperation(value = "审核数据导出", notes = "审核导出接口")
    @PostMapping(value = "/exportApprovalScheduleDetail")
    public void exportJob(@RequestBody ApprovalTransportJobVO jobVO, HttpServletResponse reqponse) {
        approvalTransportJobWebService.exportApprovalScheduleDetail(jobVO, reqponse);
    }

    /**
     * 办件查询任务列表
     *
     * @param jobVO jobVO
     * @return list
     */
    @ApiOperation(value = "办件查询任务列表", notes = "办件查询列表接口")
    @RequestMapping(value = "/findListByPage", method = RequestMethod.POST)
    public JSONObject findJobListByPage(@RequestBody TransportNewJobVO jobVO) {
        return this.basicReturnJson(jobVO, ApprovalTransportJobWebService.class,
                (param, service) -> approvalTransportJobWebService.findJobListByPage(jobVO));
    }

    /**
     * 办件查询任务详情列表
     */
    @ApiOperation(value = "办件查询任务详情列表", notes = "办件查询任务详情接口")
    @RequestMapping(value = "/findDetailByPage", method = RequestMethod.POST)
    public JSONObject findDetailByPage(@RequestBody ApprovalTransportJobVO jobVO){
        return this.basicReturnJson(jobVO, ApprovalTransportJobWebService.class,
                (param, service) -> approvalTransportJobWebService.findDetailByPage(jobVO));
    }


    /**
     * 查看待办任务
     */
    @ApiOperation(value = "查看待办事项", notes = "查看待办事项接口")
    @RequestMapping(value = "/findSchedual", method = RequestMethod.GET)
    public JSONObject findSchedual(@RequestHeader("token")String token){
        return this.basicReturnJson(token, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findSchedual(token));
    }

    @ApiOperation(value = "分页查询待办事项审核详情", notes = "分页查询待办事项审核详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobVO", value = "approvalTransportJobVO", required = true, paramType = "body", dataType = "approvalTransportJobVO")
    })
    @PostMapping(value = "/findPendingDetailByWhere")
    public JSONObject findPendingDetailByWhere(@RequestBody ApprovalTransportJobVO jobVO) {
        return this.basicReturnJson(jobVO, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findPendingDetailByWhere(jobVO));
    }

    @ApiOperation(value = "导出待办事项审核详情", notes = "导出待办事项审核详情接口")
    @PostMapping(value = "/exportPendingDetail")
    public void exportPendingDetail(@RequestBody ApprovalTransportJobVO jobVO, HttpServletResponse reqponse) {
        approvalTransportJobWebService.exportPendingDetail(jobVO, reqponse);
    }

    @ApiOperation(value = "分页查询基站对应扇区详情", notes = "分页查询基站对应扇区详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalScheduleVO", value = "approvalScheduleVO", required = true, paramType = "body", dataType = "approvalScheduleVO")
    })
    @PostMapping(value = "/findCellDetail")
    public JSONObject findCellDetail(@RequestBody ApprovalScheduleVO vo) {
        return this.basicReturnJson(vo, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findCellDetail(vo));
    }

}
