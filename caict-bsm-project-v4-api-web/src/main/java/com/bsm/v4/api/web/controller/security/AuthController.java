package com.bsm.v4.api.web.controller.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.controller.config.BasicController;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.vo.security.UsersVO;
import com.caictframework.utils.util.JSONResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @Title: AuthController
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.controller.security
 * @Date 2023/8/15 14:33
 * @description:
 */
@RestController
@RequestMapping(value = "/apiWeb/security/auth")
@Api(value = "web端-系统-权限接口", tags = "web端-系统-权限接口")
public class AuthController extends BasicController {
    @Autowired
    private AuthWebService authWebService;

    @ApiOperation(value = "登录", notes = "登录接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersVO", value = "用户对象", required = true, paramType = "body", dataType = "usersVO")
    })
    @PostMapping(value = "/loginOn")
    public JSONObject loginOn(@RequestBody UsersVO usersVO, HttpServletRequest request) {
        return this.basicReturnJson(usersVO, AuthWebService.class, (vo, service) -> service.loginOn(usersVO.getLoginName(), usersVO.getPassword(), request));
    }

    @ApiOperation(value = "登出", notes = "登出接口")
    @GetMapping(value = "/loginOff")
    public JSONObject loginOff(@RequestHeader String token) {
        return this.basicReturnJson(token, AuthWebService.class, (vo, service) -> service.loginOff(vo));
    }

    @ApiOperation(value = "根据token获取用户登陆信息", notes = "根据token获取用户登陆信息口")
    @GetMapping(value = "/getLoginUsersDTO")
    public JSONObject getLoginUsersDTO(@RequestHeader String token) {
        return this.basicReturnJson(token, AuthWebService.class, (vo, service) -> service.getLoginUsersDTO(token));
    }

    @ApiOperation(value = "重置密码", notes = "重置密码接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersVO", value = "用户对象", required = true, paramType = "body", dataType = "usersVO")
    })
    @PostMapping(value = "/resetPassword")
    public JSONObject resetPassword(@RequestBody UsersVO usersVO) {
        return this.basicReturnJson(usersVO, AuthWebService.class, (vo, service) -> service.resetPassword(vo.getLoginName(), vo.getPassword()));

    }

    @ApiOperation(value = "修改密码", notes = "修改密码接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersVO", value = "用户对象", required = true, paramType = "body", dataType = "usersVO")
    })
    @PostMapping(value = "/updatePassword")
    public JSONObject updatePassword(@RequestHeader String token, @RequestBody UsersVO usersVO) {
        if (token != null) {
            UsersDTO usersDTOToken = authWebService.findLoginUsersDTO(token);
            if (usersDTOToken != null) {
                Map<String, Object> map = authWebService.updatePassword(usersDTOToken.getUserId(), usersVO.getPasswordOld(), usersVO.getPassword());
                if (map != null) return JSONResult.getSuccessJson(map, "修改成功,请重新登录");
                return JSONResult.getFailureJson("修改失败");
            } else {
                return JSONResult.getFailureJson("无token的用户信息");
            }
        }
        return JSONResult.getFailureJson("未识别token");
    }

}
