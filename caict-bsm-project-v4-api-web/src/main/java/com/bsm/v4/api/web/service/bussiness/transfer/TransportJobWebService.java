package com.bsm.v4.api.web.service.bussiness.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transferNew.TransportJobNewWebService;
import com.bsm.v4.api.web.service.document.HdfsWebService;
import com.bsm.v4.api.web.service.rule.BtsDataCheckRuleWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.api.web.utils.*;
import com.bsm.v4.domain.security.service.business.transfer.TransportJobBranchService;
import com.bsm.v4.domain.security.service.business.transfer.TransportJobBranchTempService;
import com.bsm.v4.domain.security.service.business.transfer.TransportJobService;
import com.bsm.v4.domain.security.service.business.transferNew.TransportJobNewService;
import com.bsm.v4.domain.security.service.security.RegionService;
import com.bsm.v4.system.model.contrust.DBBoolConst;
import com.bsm.v4.system.model.contrust.transfer.ApprovalTransportJobStateConst;
import com.bsm.v4.system.model.contrust.transfer.LogTransportJobTypeConst;
import com.bsm.v4.system.model.contrust.transfer.TransportFileStateConst;
import com.bsm.v4.system.model.contrust.transfer.TransportJobStateConst;
import com.bsm.v4.system.model.dto.business.transfer.*;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.*;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobNew;
import com.bsm.v4.system.model.entity.message.Message;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobConfirmVO;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.data.util.SnowflakeManager;
import com.caictframework.utils.repository.MyRestRepository;
import com.caictframework.utils.service.RedisService;
import com.caictframework.utils.util.JSONResult;
import com.caictframework.utils.util.VerificationCode;
import com.opencsv.bean.CsvToBeanBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.*;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class TransportJobWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(TransportJobWebService.class);

    @Autowired
    private AuthWebService authWebService;

    @Autowired
    private TransportJobBranchWebService transportJobBranchWebService;

    @Autowired
    private TransportJobService transportJobService;

    @Autowired
    private TransportJobNewWebService transportJobNewWebService;

    @Autowired
    private TransportFileWebService transportFileWebService;

    @Autowired
    private TransportRawBtsWebService transportRawBtsWebService;

    @Autowired
    private HdfsWebService hdfsWebService;

    @Autowired
    private TransportScheduleWebService transportScheduleWebService;

    @Autowired
    private TransportRawBtsDealWebService transportRawBtsDealWebService;

    @Autowired
    private TransportJobBranchTempWebService transportJobBranchTempWebService;

    @Autowired
    private TransportRawBtsDealLogWebService transportRawBtsDealLogWebService;

    @Autowired
    private MyRestRepository<Message> myRestRepository;

    @Autowired
    private AsyncRawBtsWebService asyncRawBtsWebService;

    @Autowired
    private ApprovalRawBtsWebService approvalRawBtsWebService;

    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;

    @Autowired
    private LogTransportJobWebService logTransportJobWebService;

    @Autowired
    private TransportJobBranchTempService transportJobBranchTempService;

    @Autowired
    private BtsDataCheckRuleWebService btsDataCheckRuleService;

    @Autowired
    private TransportJobBranchService transportJobBranchService;

    @Autowired
    private SnowflakeManager snowflakeManager;
    @Autowired
    private TransportJobNewService transportJobNewService;

    @Autowired
    private ApprovalScheduleWebService approvalScheduleWebService;

    @Autowired
    private RegionService regionService;

    @Autowired(required = false)
    private RedisService redisService;

    @Value("${caict.message.url}")
    private String messageUrl;
    @Value("${caict.myFilePath}")
    private String myFilePath;

    /**
     * 新建中转任务
     */
    public Map<String, Object> createTransportJob(TransportJobVO dto, String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        //判断同一运营商上次提交任务是否处理
        if (transportJobService.selectCountByTypeStateCompare(usersDTO.getUserId(),
                TransportJobStateConst.COMPARE_COMMIT_UPLOAD, Arrays.asList(TransportJobStateConst.COMPARE_SUCCESS,
                        TransportJobStateConst.CONFIRM_PROCESSING, TransportJobStateConst.CONFIRM_PASS,
                        TransportJobStateConst.CHECK_PROCESSING)) > 0) {
            return this.basicReturnFailure("上次任务已受理，请等待");
        }

        TransportJobDTO transportJobDTO = transportJobService.findOneByJobName(usersDTO.getType(), dto.getJobName());
        //TransportJobDTO transportJobDTO = transportJobService.findOneByJobId(usersDTO.getType(), dto.getJobId());
        if (transportJobDTO != null || dto.getJobId() != null) {
            if (!transportJobDTO.getIsCompare().isEmpty() || "0".equals(transportJobDTO.getIsCompare())) {
                //更新任务名称
                transportJobService.upTransportJobName(dto);
                //重新创建文件与任务关系
                List<String> fileIds = getFileIds(dto);
                transportJobService.associationSomeId(fileIds, dto.getJobId());
                deleteFileForUp(fileIds, dto.getJobId());
                return this.basicReturnSuccess(transportJobDTO);

            } else {
                return this.basicReturnFailure("此任务已经提交，请查看事项名称是否重复");
            }
        } else {
            //创建任务
            TransportJob transportJob = new TransportJob();
            transportJob.setGmtCreate(new Date());
            transportJob.setGmtModified(new Date());
            transportJob.setUserGuid(usersDTO.getUserId());
            transportJob.setJobState(TransportJobStateConst.STATE_UPLOAD);
            transportJob.setJobName(dto.getJobName());
            //如果是注销上传，则将dataType设置为前端传值
            transportJob.setDataType(dto.getDataType());
            transportJob.setIsCompare(TransportJobStateConst.COMPARE_COMMIT_UPLOAD);
            String jobGuid = transportJobService.save(transportJob).toString();
            //将文件与任务关联
            List<String> fileIds = getFileIds(dto);
            transportJobService.associationSomeId(fileIds, jobGuid);
            if (jobGuid != null) {
                transportJobDTO = new TransportJobDTO();
                BeanUtils.copyProperties(transportJob, transportJobDTO);
                transportJobDTO.setGuid(jobGuid);
                return this.basicReturnSuccess(transportJobDTO);
            }
        }
        return this.basicReturnFailure("任务创建失败");
    }

    /**
     * 解除编辑取消的文件与任务关系
     */
    private void deleteFileForUp(List<String> fileIds, String jobId) {
        //当全部清空时
        if (fileIds.isEmpty()) fileIds.add(0, "null");
        //删除取消文件
        List<String> movIdList = transportJobService.getMovFileIdForUp(fileIds, jobId);
        if (movIdList.isEmpty()) return;
        for (String fileId : movIdList) {
            hdfsWebService.deleteFile(fileId);
        }
    }

    /**
     * 获取当前需要绑定关系的文件id
     */
    private List<String> getFileIds(TransportJobVO dto) {
        if (dto.getTransportFileDTOList() == null && dto.getTransportFileAttachedDTOList() == null)
            return new ArrayList<>();
        //批量操作
        List<String> list = new ArrayList<>();
        if (dto.getTransportFileDTOList() != null)
            for (TransportFileDTO fileDTO : dto.getTransportFileDTOList()) {
                list.add(fileDTO.getFileId());
            }
        if (dto.getTransportFileAttachedDTOList() != null)
            for (TransportFileDTO mFileDTO : dto.getTransportFileAttachedDTOList()) {
                list.add(mFileDTO.getFileId());
            }
        return list;
    }

    /**
     * 提交任务
     */
    public Map<String, Object> commitTransportJob(TransportJobDTO dto, String token) {
        String jobId = dto.getJobId();
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        TransportJob transportJob = transportJobService.findById(jobId);

        //获取该job下所有fileId
        dto.setUserId(usersDTO.getUserId());
        List<String> fileIdOfJob = transportJobService.getFileIdOfJob(dto);
        if (fileIdOfJob.isEmpty()) return this.basicReturnFailure("未上传相关文件！");
        //判断能否提交
        String errorMessage = checkProcessState(transportJob, jobId);
        if (errorMessage != null) return this.basicReturnFailure(errorMessage);
        //初始化规则
        btsDataCheckRuleService.init();
        //开启线程进行文件提交处理
//        ThreadPoolUtil.getThread().execute(() -> fileDeal(fileIdOfJob, usersDTO, transportJob));

        transportJob.setIsCompare(TransportJobStateConst.COMPARE_PROCESSING);
        transportJob.setJobDate(new Date());
        transportJobService.update(transportJob);
        return this.basicReturnSuccess("提交任务处理中");
    }

    //文件处理方法
    public void fileDeal(List<String> fileIdOfJob, UsersDTO usersDTO, TransportJob job) {
        for (String fileId : fileIdOfJob) {
            //将csv文件的状态改成校验中
            String fileSave = transportFileWebService.updateFileStateByFileId(fileId, TransportFileStateConst.CHECK_PROCESSING);

            if (fileSave != null) {
                //导入原始数据
                boolean ret = readRawData(fileId, usersDTO, job.getGuid());
                if (!ret) {
                    //失败则将文件状态修改为校验失败
                    TransportFile file = transportFileWebService.findById(fileId);
                    file.setStatus(TransportFileStateConst.CHECK_FAIL);
                    transportFileWebService.update(file);
                }
            }
        }

        //判断job下所有文件都已经处理
//        transportFileWebService.fileStateConst(job.getGuid(), TransportFileStateConst.OPERATORS);
        //任务提交处理
        commit(job.getGuid(), usersDTO);

    }

    /**
     * CSV原始数据读取
     *
     * @param fileId
     * @param usersDTO
     */
    private boolean readRawData(String fileId, UsersDTO usersDTO, String jobId) {
        String orgType = usersDTO.getType();
        //获取校验规则
        JSONObject checkRuleRedisJson = JSONObject.parseObject(redisService.get("Caict:checkRule:netTs"));
        JSONObject netTsJson = checkRuleRedisJson.getJSONObject(orgType);

        String orgRegionJson = redisService.get("Caict:regionCounty:orgRegion");
        if (orgRegionJson == null) {
            LOG.error("未获取到系统所属省的所有行政区信息");
            return false;
        }

        TransportFile transportFile = transportFileWebService.findById(fileId);
        if (transportFile == null) return false;
        TransportJob transportJob = transportJobService.findById(transportFile.getJobId());
        if (transportJob == null) return false;
        if ("2".equals(String.valueOf(transportFile.getStatus()))) {
            logTransportJobWebService.addLog(transportJob.getGuid(), LogTransportJobTypeConst.ERROR, "csv文件异常", "csv文件已经处理", fileId);
            return false;
        }

        try {
            Pattern isNum = Pattern.compile("^\\d+(\\.\\d+)?$");
            //从csv文件中读取数据
            InputStream fileInputStream = hdfsWebService.getFileInputStream(fileId);
            Reader reader = new InputStreamReader(fileInputStream, "GBK");
            List<TransportRawBtsCsvDTO> transportRawBtsCsvDTOList = new CsvToBeanBuilder<TransportRawBtsCsvDTO>(reader).withType(TransportRawBtsCsvDTO.class).build().parse();
            reader.close();
            fileInputStream.close();
            //序列化,否者多管道处理将导致数据丢失
            List<TransportRawBts> transportRawBtsList = Collections.synchronizedList(new ArrayList<TransportRawBts>());
            List<LogTransportJob> logTransportJobList = Collections.synchronizedList(new ArrayList<LogTransportJob>());

            //记录file数据条数
//            transportFile.setDataNum(String.valueOf(transportRawBtsCsvDTOList.size()));
            transportFileWebService.update(transportFile);

            //根据cellId去重
            List<TransportRawBtsCsvDTO> transportRawBtsDTOListR = transportRawBtsCsvDTOList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TransportRawBtsCsvDTO::getCellId))), ArrayList::new)
            );

            List<String> errList = new ArrayList<String>();//校验连坐及所有错误bts
            transportRawBtsDTOListR.parallelStream().forEach(t -> {
                TransportRawBts transportRawBts = new TransportRawBts();
                //转换bean
                BeanUtils.copyProperties(t, transportRawBts);
                transportRawBts.setTrfDate(DateUtils.parseDate(t.getTrfDate()));
                transportRawBts.setTrfData(isNum.matcher(t.getTrfData()).matches() ? Double.parseDouble(t.getTrfData()) : 0);
                transportRawBts.setTrfUser(isNum.matcher(t.getTrfUser()).matches() ? Double.parseDouble(t.getTrfUser()) : 0);
                transportRawBts.setStScene(t.getStScene());
                transportRawBts.setGuid(VerificationCode.myUUID());
                transportRawBts.setJobGuid(String.valueOf(transportJob.getGuid()));
                transportRawBts.setIsHandle("0");
                transportRawBts.setIsDownload("0");
                transportRawBts.setUserGuid(usersDTO.getUserId());
                transportRawBts.setUploadDate(new Date());
                transportRawBts.setUploadFlag("1");
                transportRawBts.setFileGuid(fileId);
                transportRawBts.setOrgType(orgType);
                // 修改成数字
                if ("直放站".equals(transportRawBts.getExpandStation())) {
                    transportRawBts.setExpandStation("2");
                } else if ("宏站".equals(transportRawBts.getExpandStation())) {
                    transportRawBts.setExpandStation("1");
                }
                if ("室内站".equals(transportRawBts.getAttributeStation())) {
                    transportRawBts.setAttributeStation("1");
                } else if ("室外站".equals(transportRawBts.getAttributeStation())) {
                    transportRawBts.setAttributeStation("2");
                }

                //校验参数
                Map<String, Object> parameterErrorMessageMap = BtsDataUtil.validParameter(transportRawBts, netTsJson, orgRegionJson);
                String isValid = (String) parameterErrorMessageMap.get("isValid");
                if ("4".equals(isValid)) {
                    errList.add(transportRawBts.getBtsId());//异常的btsNo存放
                    List<String> parameterErrorMessageList = (List) parameterErrorMessageMap.get("parameterErrorMessage");
                    for (String parameterErrorMessage : parameterErrorMessageList) {
                        //写入错误日志
//                        logTransportJobList.add(logTransportJobWebService.addLogValueAsNew(transportJob.getGuid(), LogTransportJobTypeConst.ERROR, "文件格式错误", "基站id（" + transportRawBts.getBtsId() + "）的数据参数校验。" + parameterErrorMessage, fileId, transportRawBts));
                    }
                    transportRawBts.setIsValid(DBBoolConst.WARNING);
                } else {
                    //检查频段
                    NetTsValidDTO netTsValidDTO = btsDataCheckRuleService.validParameter(transportRawBts, usersDTO.getType());
                    if (netTsValidDTO.getMessage() != null) {
                        errList.add(transportRawBts.getBtsId());//异常的btsNo存放
//                        logTransportJobList.add(logTransportJobWebService.addLogValueAsNew(transportRawBts.getJobGuid(), LogTransportJobTypeConst.ERROR, "数据校验过程存在异常", netTsValidDTO.getMessage(), transportRawBts.getFileGuid(), transportRawBts));
                        Calendar cal = Calendar.getInstance();
                        cal.add(Calendar.YEAR, 3);
                        transportRawBts.setExpireDate(cal.getTime());
                        transportRawBts.setIsValid(DBBoolConst.DOUBT);
                    } else {
                        transportRawBts.setExpireDate(netTsValidDTO.getExpireDate());
                        transportRawBts.setIsValid(DBBoolConst.TURE);
                    }

                    //判断制式插入
                    if (getGen(usersDTO.getType(), transportRawBts.getTechType(), transportRawBts.getJobGuid(), transportRawBts.getFileGuid(), transportRawBts, logTransportJobList) != null) {
                        transportRawBts.setIsValid(DBBoolConst.WARNING);
                    }
                }
                //全部数据
                transportRawBtsList.add(transportRawBts);
            });
            //校验异常与频段异常连坐
            List<LogTransportJob> toAdd = new ArrayList<>();
            Set<String> errBtsIds = new HashSet<>(errList);
            synchronized (logTransportJobList) {
                Iterator<TransportRawBts> iterator = transportRawBtsList.iterator();
                while (iterator.hasNext()) {
                    TransportRawBts next = iterator.next();
                    if (errBtsIds.contains(next.getBtsId())) {
                        toAdd.add(logTransportJobWebService.addLogValueAsNew(String.valueOf(transportJob.getGuid()),
                                LogTransportJobTypeConst.ERROR, "相关数据错误", "基站id(" +
                                        next.getBtsId() + ")的数据参数校验。相关数据异常",
                                fileId, next));
                        //清楚list中错误数据
                        iterator.remove();
                    }
                }
                logTransportJobList.addAll(toAdd);
            }

            //存入数据库
//            importRawData(fileId, errList, jobId);
            //添加日志
//            if (logTransportJobList.size() > 0) {
//                //添加日志
//                logTransportJobWebService.insertBatch(logTransportJobList);
//            }
            if (transportRawBtsList.size() > 0) {
                //添加数据
                transportRawBtsWebService.insertBatch(transportRawBtsList);
            }
            //查询基站基础数据不一致的基站识别号
            List<String> unsameList = transportRawBtsWebService.findAllBtsId(String.valueOf(transportFile.getJobId()), String.valueOf(transportFile.getId()));
            //判断宏站中室内室外站不一致的数据
//                    List<TransportRawBtsDTO> transportRawBtsDTOList1 = transportRawBtsWebService.findAllBtsIdExpend(transportFile.getJobGuid(), String.valueOf(transportFile.getGuid()));
            //批量修改数据
            transportRawBtsWebService.updateIsValid(String.valueOf(transportFile.getJobId()), String.valueOf(transportFile.getId()), DBBoolConst.WARNING);

            //更新文件为已处理
            transportFile.setStatus(TransportFileStateConst.CHECK_SUCCESS);
            transportFileWebService.update(transportFile);

            //保存正确业务数据并记录日志
            if (!transportRawBtsList.isEmpty()) {
                Set<String> dangerSet = new HashSet<>(unsameList);
//                transportRawBtsList.removeIf(bts -> dangerSet.contains(bts.getBtsId()));
                Iterator<TransportRawBts> iterator = transportRawBtsList.iterator();
                while (iterator.hasNext()) {
                    TransportRawBts next = iterator.next();
                    String btsId = next.getBtsId();
                    if (dangerSet.contains(btsId)) {
                        logTransportJobList.add(logTransportJobWebService.addLogValueAsNew(String.valueOf(transportFile.getJobId()), LogTransportJobTypeConst.ERROR, "文件格式错误", "基站识别号（" + next.getBtsId() + "）的基础数据：识别号、名称、技术体制、地址、经纬度、海拔高度、所属区域。其中部分数据不一致", fileId, next));
                        //清楚list中错误数据
                        iterator.remove();
                    }
                }
                //List<TransportRawBtsDealLog> transportRawBtsDealLogList = changeModelToDealLog(allData);
                if (!transportRawBtsList.isEmpty()) {
                    //实体类转换
                    List<TransportRawBtsDeal> dealList = changeModelToDeal(transportRawBtsList);
                    transportRawBtsDealWebService.insertBatch(dealList);
                    //transportRawBtsDealLogWebService.insertBatch(transportRawBtsDealLogList);
                }
            }

            //添加日志
            if (logTransportJobList.size() > 0) {
                logTransportJobWebService.insertBatch(logTransportJobList);
            }

            //记录校验情况及数量
            if (logTransportJobList.size() > 0 && transportRawBtsList.size() > 0) {
                //部分通过
                TransportJobDTO dto = new TransportJobDTO();
                dto.setJobId(jobId);
                dto.setCheckSuccessNum(transportRawBtsList.size());
                dto.setCheckFailedNum(logTransportJobList.size());
                transportJobService.upJobCheckStatus(TransportJobStateConst.STATE_PART_SUCCESS, dto);
            } else if (logTransportJobList.size() == 0 && transportRawBtsList.size() > 0) {
                //全部通过
                TransportJobDTO dto = new TransportJobDTO();
                dto.setJobId(jobId);
                dto.setCheckSuccessNum(transportRawBtsList.size());
                dto.setCheckFailedNum(0);
                transportJobService.upJobCheckStatus(TransportJobStateConst.STATE_SUCCESS, dto);
            } else {
                //全部失败或异常
                TransportJobDTO dto = new TransportJobDTO();
                dto.setJobId(jobId);
                dto.setCheckSuccessNum(0);
                dto.setCheckFailedNum(logTransportJobList.size());
                transportJobService.upJobCheckStatus(TransportJobStateConst.STATE_FAILED, dto);
            }

            //清理
            errList.clear();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            StringBuffer errorDetailStringBuffer = new StringBuffer("文件格式不符合《综合资源系统与无委外部接口需求说明书》。");
            logTransportJobWebService.addLog(String.valueOf(transportJob.getGuid()), LogTransportJobTypeConst.ERROR, "csv文件格式错误", errorDetailStringBuffer.toString(), fileId);
            transportFile.setStatus(TransportFileStateConst.UPLOAD_FAILURE);
            transportFileWebService.update(transportFile);
            return false;
        }
    }

    /**
     * 下载需读取文件并返回路径
     *
     * @return
     */
    private String downLoadReadFile(String fileId) {
        Map<String, Object> stringObjectMap = hdfsWebService.downloadFile(fileId);
        return stringObjectMap.get("data").toString();
    }

//    //存入数据库
//    private void importRawData(String fileId, List<String> errList, String jobId) {
//        ProcessingDataDTO processingDataDTO = JSONObject.parseObject(redisService.rpop(redisKey), ProcessingDataDTO.class);
//        if (processingDataDTO != null) {
//            TransportFile transportFile = transportFileWebService.findById(fileId);
//            List<TransportRawBts> transportRawBtsList = processingDataDTO.getTransportRawBtsList();
//            List<LogTransportJob> logTransportJobList = processingDataDTO.getLogTransportJobList();
//            int successNum = 0;
//            if (logTransportJobList != null && logTransportJobList.size() > 0) {
//                //添加日志
//                logTransportJobWebService.insertBatch(logTransportJobList);
//            }
//            if (transportRawBtsList!=null) {
//                successNum=transportRawBtsList.size();
//                //添加数据
//                transportRawBtsWebService.insertBatch(transportRawBtsList);
//            }
//
//            //获取当前批处理数量
//            RedisLock redisLock = redisService.getRedisLock(redisKey + "number");
//            if (redisLock != null) {
//                int page = (int) redisLock.getContent();
//                page -= 1;
//                redisLock.setContent(page);
//                redisLock.setLock("1");
//                //处理数量减1，并解锁
//                redisService.set(redisKey + "number", JSONObject.toJSONString(redisLock));
//
//                //已经处理完毕
//                if (page == 0) {
//                    //查询基站基础数据不一致的基站识别号
//                    List<TransportRawBtsDTO> transportRawBtsDTOList = transportRawBtsWebService.findAllBtsId(transportFile.getJobGuid(), String.valueOf(transportFile.getGuid()));
//                    //判断宏站中室内室外站不一致的数据
////                    List<TransportRawBtsDTO> transportRawBtsDTOList1 = transportRawBtsWebService.findAllBtsIdExpend(transportFile.getJobGuid(), String.valueOf(transportFile.getGuid()));
//                    //异常集合
//                    List<LogTransportJob> logTransportJobBtsIdList = new ArrayList<>();
//                    if (transportRawBtsDTOList != null && transportRawBtsDTOList.size() > 0) {
//                        //插入异常
//                        for (TransportRawBtsDTO transportRawBtsDTO : transportRawBtsDTOList) {
//                            errList.add(transportRawBtsDTO.getBtsId());
//                            logTransportJobBtsIdList.add(logTransportJobWebService.addLogValueAsNew(transportFile.getJobGuid(), LogTransportJobTypeConst.ERROR, "文件格式错误", "基站识别号（" + transportRawBtsDTO.getBtsId() + "）的基础数据：识别号、名称、技术体制、地址、经纬度、海拔高度、所属区域。其中部分数据不一致", fileId, transportRawBtsDTO));
//                        }
//                    }
////                    if (transportRawBtsDTOList1 != null && transportRawBtsDTOList1.size() > 0) {
////                        //插入异常
////                        for (TransportRawBtsDTO transportRawBtsDTO : transportRawBtsDTOList1) {
////                            errList.add(transportRawBtsDTO.getBtsId());
////                            logTransportJobBtsIdList.add(logTransportJobWebService.addLogValueAsNew(transportFile.getJobGuid(), LogTransportJobTypeConst.ERROR, "文件格式错误", "基站识别号（" + transportRawBtsDTO.getBtsId() + "）的基础数据：宏站下既有室内站又有室外站错误", fileId, transportRawBtsDTO));
////                        }
////                    }
//
//                    //添加日志
//                    if (logTransportJobBtsIdList.size() > 0) {
//                        logTransportJobWebService.insertBatch(logTransportJobBtsIdList);
//                    }
//                    //批量修改数据
//                    transportRawBtsWebService.updateIsValid(transportFile.getJobGuid(), String.valueOf(transportFile.getGuid()), DBBoolConst.WARNING);
//
//                    //更新文件为已处理
//                    transportFile.setFileState(TransportFileStateConst.CHECK_SUCCESS);
//                    transportFileWebService.update(transportFile);
//
//                    //记录校验情况及数量
//                    if(errList.size() > 0 && successNum>0){
//                        //部分通过
//                        TransportJobDTO dto = new TransportJobDTO();
//                        dto.setJobId(jobId);
//                        dto.setCheckSuccessNum(successNum);
//                        dto.setCheckFailedNum(errList.size());
//                        transportJobService.upJobCheckStatus(TransportJobStateConst.STATE_PART_SUCCESS,dto);
//                    }else if(errList.size() == 0  &&  successNum > 0){
//                        //全部通过
//                        TransportJobDTO dto = new TransportJobDTO();
//                        dto.setJobId(jobId);
//                        dto.setCheckSuccessNum(successNum);
//                        dto.setCheckFailedNum(0);
//                        transportJobService.upJobCheckStatus(TransportJobStateConst.STATE_SUCCESS,dto);
//                    }else{
//                        //全部失败或异常
//                        TransportJobDTO dto = new TransportJobDTO();
//                        dto.setJobId(jobId);
//                        dto.setCheckSuccessNum(0);
//                        dto.setCheckFailedNum(errList.size());
//                        transportJobService.upJobCheckStatus(TransportJobStateConst.STATE_FAILED,dto);
//                    }
//
//                    //删除当前批处理数量
//                    redisService.delete(redisKey + "number");
//                }
//            }
//
//            //保存正确业务数据并记录日志
//            if (!transportRawBtsList.isEmpty()) {
//                List<TransportRawBts> allData = new ArrayList<>(transportRawBtsList);
//                Set<String> dangerSet = new HashSet<>(errList);
//                allData.removeIf(bts -> dangerSet.contains(bts.getBtsId()));
//                //实体类转换
//                List<TransportRawBtsDeal> errListAndLog = changeModelToDeal(allData);
//                //List<TransportRawBtsDealLog> transportRawBtsDealLogList = changeModelToDealLog(allData);
//                if (!errListAndLog.isEmpty()) {
//                    transportRawBtsDealWebService.insertBatch(errListAndLog);
//                    //transportRawBtsDealLogWebService.insertBatch(transportRawBtsDealLogList);
//                }
//            }
//        }
//    }

    //deal表实体类转换
    private List<TransportRawBtsDeal> changeModelToDeal(List<TransportRawBts> errListAndLogMid) {
        List<TransportRawBtsDeal> list = new ArrayList<>();
        for (TransportRawBts data : errListAndLogMid) {
            TransportRawBtsDeal model = new TransportRawBtsDeal();
            //转换bean
            //BeanUtils.copyProperties(errListAndLogMid, model);
            model.setGuid(data.getGuid());
            model.setJobGuid(data.getJobGuid());
            model.setIsValid(data.getIsValid());
            model.setCellName(data.getCellName());
            model.setCellId(data.getCellId());
            model.setBtsName(data.getBtsName());
            model.setBtsId(data.getBtsId());
            model.setTechType(data.getTechType());
            model.setLocation(data.getLocation());
            model.setLongitude(data.getLongitude());
            model.setLatitude(data.getLatitude());
            model.setSendStartFreq(data.getSendStartFreq());
            model.setSendEndFreq(data.getSendEndFreq());
            model.setAccStartFreq(data.getAccStartFreq());
            model.setAccEndFreq(data.getAccEndFreq());
            model.setMaxEmissivePower(data.getMaxEmissivePower());
            model.setHeight(data.getHeight());
            model.setDataType(data.getDataType());
            model.setCounty(data.getCounty());
            model.setRegionCode(data.getRegionCode());
            model.setIsHandle(data.getIsHandle());
            model.setUploadDate(data.getUploadDate());
            model.setIsDownload(data.getIsDownload());
            model.setUserGuid(data.getUserGuid());
            model.setVendorName(data.getVendorName());
            model.setDeviceModel(data.getDeviceModel());
            model.setModelCode(data.getModelCode());
            model.setAntennaGain(data.getAntennaGain());
            model.setAntennaModel(data.getAntennaModel());
            model.setAntennaFactory(data.getAntennaFactory());
            model.setPolarizationMode(data.getPolarizationMode());
            model.setAntennaAzimuth(data.getAntennaAzimuth());
            model.setFeederLoss(data.getFeederLoss());
            model.setAltitude(data.getAltitude());
            model.setGenNum(data.getGenNum());
            model.setSetYear(data.getSetYear());
            model.setSetMonth(data.getSetMonth());
            model.setOrgType(data.getOrgType());
            model.setExpandStation(data.getExpandStation());
            model.setAttributeStation(data.getAttributeStation());
            model.setAtRang(data.getAtRang());
            model.setAtEang(data.getAtEang());
            model.setStServR(data.getStServR());
            model.setTrfData(data.getTrfData());
            model.setTrfDate(data.getTrfDate());
            model.setStScene(data.getStScene());
            model.setTrfUser(data.getTrfUser());
            list.add(model);
        }
        return list;
    }

    //deal_log实体类转换
    private List<TransportRawBtsDealLog> changeModelToDealLog(List<TransportRawBts> errListAndLogMid) {
        List<TransportRawBtsDealLog> list = new ArrayList<>();
        for (TransportRawBts data : errListAndLogMid) {
            TransportRawBtsDealLog model = new TransportRawBtsDealLog();
            //转换bean
            //BeanUtils.copyProperties(errListAndLogMid, model);
            model.setGuid(String.valueOf(data.getGuid()));
            model.setJobGuid(data.getJobGuid());
            model.setIsValid(data.getIsValid());
            model.setCellName(data.getCellName());
            model.setCellId(data.getCellId());
            model.setBtsName(data.getBtsName());
            model.setBtsId(data.getBtsId());
            model.setTechType(data.getTechType());
            model.setLocation(data.getLocation());
            model.setLongitude(data.getLongitude());
            model.setLatitude(data.getLatitude());
            model.setSendStartFreq(data.getSendStartFreq());
            model.setSendEndFreq(data.getSendEndFreq());
            model.setAccStartFreq(data.getAccStartFreq());
            model.setAccEndFreq(data.getAccEndFreq());
            model.setMaxEmissivePower(data.getMaxEmissivePower());
            model.setHeight(data.getHeight());
            model.setDataType(data.getDataType());
            model.setCounty(data.getCounty());
            model.setRegionCode(data.getRegionCode());
            model.setIsHandle(data.getIsHandle());
            model.setUploadDate(data.getUploadDate());
            model.setIsDownload(data.getIsDownload());
            model.setUserGuid(data.getUserGuid());
            model.setVendorName(data.getVendorName());
            model.setDeviceModel(data.getDeviceModel());
            model.setModelCode(data.getModelCode());
            model.setAntennaGain(data.getAntennaGain());
            model.setAntennaModel(data.getAntennaModel());
            model.setAntennaFactory(data.getAntennaFactory());
            model.setPolarizationMode(data.getPolarizationMode());
            model.setAntennaAzimuth(data.getAntennaAzimuth());
            model.setFeederLoss(data.getFeederLoss());
            model.setAltitude(data.getAltitude());
            model.setGenNum(data.getGenNum());
            model.setSetYear(data.getSetYear());
            model.setSetMonth(data.getSetMonth());
            model.setOrgType(data.getOrgType());
            model.setExpandStation(data.getExpandStation());
            model.setAttributeStation(data.getAttributeStation());
            model.setAtRang(data.getAtRang());
            model.setAtEang(data.getAtEang());
            model.setStServR(data.getStServR());
            list.add(model);
        }
        return list;
    }

    /**
     * 设置拉远站的基站ID和名称
     *
     * @param btsDTOList btsDTOList
     * @param jobId      jobId
     * @param orgType    orgType
     */
    private void checkExpandStation(List<TransportRawBtsDTO> btsDTOList, String jobId, String orgType) {
        // 判断是否为新增或修改 根据jobID查询
        List<AsyncRawBts> layuanByList = transportRawBtsWebService.findLayuanByOrgType(orgType);
        // 判断拉远站
        Map<String, List<TransportRawBts>> maps = new HashMap<>();
        String[] btsIdArr = btsDTOList.stream().map(TransportRawBtsDTO::getBtsId).distinct().toArray(String[]::new);
        // 根据btsId查询当前btsId所有扇区详细数据，btsIds超过1000要分批查询
        List<TransportRawBts> transportRawBts = new ArrayList<>();
        List<String> btsIds = Arrays.asList(btsIdArr);
        // 页数
        int pageNum = btsIds.size() % 3000 == 0 ? btsIds.size() / 3000 : btsIds.size() / 3000 + 1;
        // 查询基站信息
        for (int i = 1; i <= pageNum; i++) {
            List<String> btsids;
            if (i == pageNum) {
                btsids = btsIds.subList((i - 1) * 3000, btsIds.size());
            } else {
                btsids = btsIds.subList((i - 1) * 3000, i * 3000);
            }
            List<TransportRawBts> transportRawBtsList = transportRawBtsWebService.findAllByBtsIds((btsids.toArray(new String[0])), jobId);
            transportRawBts.addAll(transportRawBtsList);
        }

        // 将基站ID和基站对应起来
        for (TransportRawBts transportRawBt : transportRawBts) {
            if (maps.containsKey(transportRawBt.getBtsId())) {
                List<TransportRawBts> strList = new ArrayList<>(maps.get(transportRawBt.getBtsId()));
                strList.add(transportRawBt);
                maps.put(transportRawBt.getBtsId(), strList);
            } else {
                maps.put(transportRawBt.getBtsId(), Collections.singletonList(transportRawBt));
            }
        }
        transportRawBts.clear();

        // 取出map中的数据
        for (Map.Entry<String, List<TransportRawBts>> map : maps.entrySet()) {
            List<TransportRawBts> btsList = map.getValue();
            // 判断是否全一样
            boolean distinct = btsList.stream().allMatch(t -> t.getExpandStation() != null && (t.getExpandStation().equals("2") || t.getExpandStation().equals("直放站")));
            if (distinct) {
                // 全部一样，证明都是拉远站
                // 如果基站ID相同，但经纬度不同，拆分list，每个list中的数据为一个不同的基站
                splitListAndChangeData(btsList, transportRawBts, layuanByList);
            } else {
                // 非拉远站数量
                List<TransportRawBts> listFalse = new ArrayList<>();
                // 拉远站
                List<TransportRawBts> listTrue = new ArrayList<>();
                for (TransportRawBts rawBts : btsList) {
                    // 判断是否是拉远站，添加到相应的list中
                    if ("2".equals(rawBts.getExpandStation()) || "直放站".equals(rawBts.getExpandStation())) {
                        listTrue.add(rawBts);
                    } else {
                        listFalse.add(rawBts);
                    }
                }
                // 全都不是拉远站，无需修改基站信息，退出当前循环
                if (listFalse.size() == btsList.size()) {
                    continue;
                }
                // 部分是拉远站，判断拉远站中的制式、经纬度、海拔、地区、台址等信息；非拉远站不用管
                // TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE
                if (listTrue.size() > 0) {
                    // 如果基站ID相同，但经纬度不同，拆分list，每个list中的数据为一个不同的基站
                    splitListAndChangeData(listTrue, transportRawBts, layuanByList);
                }
            }
        }
        // 更新数据
        for (TransportRawBts transportRawBt : transportRawBts) {
            transportRawBtsWebService.update(transportRawBt);
        }
    }

    /**
     * 任务提交处理
     */
    @Transactional
    public void commit(String jobId, UsersDTO usersDTO) {
        TransportJobDTO transportJobOld = transportJobService.findOneByUserTypeAndCompareNotJobId(usersDTO.getType(),
                TransportJobStateConst.COMPARE_PROCESSING, jobId);
        //如果代办中已经存在该运营商数据但并没有确认，则将代办清空
        if (transportJobOld != null) {
            transportScheduleWebService.deleteByJobId(Long.valueOf(transportJobOld.getGuid()));
            //同时删除deal
            transportRawBtsDealWebService.deleteByJobId(String.valueOf(transportJobOld.getGuid()));
            //修改任务状态为已处理
            transportJobOld.setIsCompare(TransportJobStateConst.COMPLETE);//提交成功
            TransportJob transportJob = new TransportJob();
            BeanUtils.copyProperties(transportJobOld, transportJob);
            transportJobService.save(transportJob);
        }
        //进行数据比对
        scheduleData(jobId, usersDTO.getId().toString());
    }

    /**
     * 根据list返回对象中的某个字段的不同拆分成多个list
     * 如果字段值一样，则返回的外层list只有一个，否则有多个
     *
     * @param splitList       被拆分的list
     * @param transportRawBts 保存数据的list
     * @param layuanByList    上次提交的数据
     */
    private void splitListAndChangeData(List<TransportRawBts> splitList, List<TransportRawBts> transportRawBts, List<AsyncRawBts> layuanByList) {
        Set<String> latitudes = new HashSet<>();
        // 根据经纬度分组
        for (TransportRawBts item : splitList) {
            latitudes.add(item.getLatitude() + item.getTechType() + item.getLongitude() + item.getAltitude() + item.getBtsName() + item.getLocation() + item.getCounty() + item.getStServR());
        }
        List<TransportRawBts> tempList;
        for (String latitude : latitudes) {
            tempList = new ArrayList<>();
            // 分组判断
            for (TransportRawBts item : splitList) {
                if (latitude.equals(item.getLatitude() + item.getTechType() + item.getLongitude() + item.getAltitude() + item.getBtsName() + item.getLocation() + item.getCounty() + item.getStServR())) {
                    tempList.add(item);
                }
            }
            String num = "_GZL" + VerificationCode.codeGet(7);
            if (layuanByList.size() <= 0) {
                // 新增
                for (TransportRawBts rawBt : tempList) {
                    rawBt.setBtsId(rawBt.getBtsId() + num);
                    rawBt.setBtsName(rawBt.getBtsName().contains("(直放站)") ? rawBt.getBtsName() : rawBt.getBtsName() + "(直放站)");
                }
            }
            // 以btsId来判断新增还是变更 btsId相同，cellId不同，为变更
            List<TransportRawBts> tempRawBtsList = new ArrayList<>();
            List<String> cellIdList;
            for (TransportRawBts rawBt : tempList) {
                tempRawBtsList = new ArrayList<>();
                cellIdList = new ArrayList<>();
                for (int i = 0; i < layuanByList.size(); i++) {
                    AsyncRawBts async = layuanByList.get(i);
                    if (async.getBtsId().contains(rawBt.getBtsId())) {
                        if ((rawBt.getCellId() + rawBt.getLatitude() + rawBt.getTechType() + rawBt.getLongitude() + rawBt.getAltitude() + (rawBt.getBtsName().contains("(直放站)") ? rawBt.getBtsName().split("\\(直放站\\)")[0] : rawBt.getBtsName()) + rawBt.getLocation() + rawBt.getCounty())
                                .equals(async.getCellId() + async.getLatitude() + async.getTechType() + async.getLongitude() + async.getAltitude() + async.getBtsName().split("\\(直放站\\)")[0] + async.getLocation() + async.getCounty())) {
                            // 相同
                            rawBt.setBtsId(async.getBtsId());
                            rawBt.setBtsName(async.getBtsName());
                            layuanByList.remove(i);
                            cellIdList.add(rawBt.getCellId());
                            break;
                        } else {
                            // 不同 if-else 会有相同的被存进来
                            tempRawBtsList.add(rawBt);
                        }
                    } else {
                        // 判断是否最后一条
                        if (i == layuanByList.size() - 1) {
                            // btsId不同 新增
                            // btsId 新增
                            if (!rawBt.getBtsName().contains("(直放站)")) {
                                rawBt.setBtsId(rawBt.getBtsId() + num);
                                rawBt.setBtsName(rawBt.getBtsName().contains("(直放站)") ? rawBt.getBtsName() : rawBt.getBtsName() + "(直放站)");
                            }
                        }
                    }
                }
                // 筛选出不同的 疑似该基站下新增的扇区
                for (int i = 0; i < tempRawBtsList.size(); i++) {
                    TransportRawBts rawBts = tempRawBtsList.get(i);
                    if (cellIdList.size() > 0) {
                        for (String cellId : cellIdList) {
                            if (rawBts.getCellId().equals(cellId)) {
                                tempRawBtsList.remove(i);
                                i--;
                                break;
                            }
                        }
                    }
                }
            }
            // 更新新增扇区的数据
            for (TransportRawBts rawBts : tempRawBtsList) {
                for (TransportRawBts bts : tempList) {
                    if (bts.getCellId().equals(rawBts.getCellId())) {
                        // btsId 新增
                        String[] split = bts.getBtsId().split("_GZL");
                        if (!bts.getBtsName().contains("(拉远站)") && split.length == 2 && split[1].length() != 7) {
                            bts.setBtsId(bts.getBtsId() + num);
                            bts.setBtsName(bts.getBtsName().contains("(直放站)") ? bts.getBtsName() : bts.getBtsName() + "(直放站)");
                        }
                        break;
                    }
                }
            }
            if (tempList.size() > 0) {
                transportRawBts.addAll(tempList);
            }
        }
    }

    /**
     * 根据id修改业务状态
     */
    public String updateIsCompareById(Long guid, String isCompare) {
        TransportJob transportJob = new TransportJob();
        transportJob.setGuid(String.valueOf(guid));
        transportJob.setIsCompare(isCompare);
        return transportJobService.save(transportJob).toString();
    }

    //判断是否能处理
    protected String checkProcessState(TransportJob transportJob, String jobId) {
        if (transportJob != null && "0".equals(transportJob.getIsCompare())) {
            //判断有无csv文件
            if (transportFileWebService.selectCountByJobFileName(jobId, null) <= 0) {
                return "未发现上传的csv文件，无法提交";
            }
            return null;
        }
        return "任务异常或者已经提交，无法提交";
    }


    public String getGen(String userType, String techType, String jobGuid, String fielGuid, TransportRawBts transportRawBts, List<LogTransportJob> logTransportJobList) {
        String genNum = btsDataCheckRuleService.genParameter(userType, techType);
        if (genNum != null) {
            transportRawBts.setGenNum(genNum);
            return null;
        } else {
            logTransportJobList.add(logTransportJobWebService.addLogValueAsNew(jobGuid, LogTransportJobTypeConst.ERROR, "数据校验过程存在异常", "未检测到制式对应的信号", fielGuid, transportRawBts));
            return "数据校验过程存在异常";
        }
    }

    /**
     * CSV代办数据入库
     */
    public JSONObject scheduleData(String jobId, String userId) {
        //处理拉远站
        System.out.println(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "：验证拉远开始");
//        checkExpandStation1(jobId, userId);
        System.out.println(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "：验证拉远结束");

        //添加待办
        transportScheduleWebService.insertByTransportRawBts(jobId);

//        //添加下载文件
//        transportRawBtsDealWebService.insertByTransportRawBts(jobId, regionCode);
        //添加日志
        transportRawBtsDealLogWebService.insertByTransportRawBts(jobId);
        //删除上传数据
        transportRawBtsWebService.deleteAllByJob(jobId);

        //入库结束后填充transport_job_branch表
        List<TransportJobBranchDTO> dataGenList = transportScheduleWebService.selectDistinctDataTypeAndGenNum(jobId);
        if (dataGenList != null && dataGenList.size() != 0) {
            String userGuid = transportJobService.findById(jobId).getUserGuid();
            List<TransportJobBranch> list = new ArrayList<>();
            for (TransportJobBranchDTO dataGen : dataGenList) {
                TransportJobBranch transportJobBranch = new TransportJobBranch();
                transportJobBranch.setGuid(String.valueOf(snowflakeManager.nextValue()));
                transportJobBranch.setJobGuid(jobId);
                transportJobBranch.setIsCompare(TransportJobStateConst.COMPARE_SUCCESS);
                transportJobBranch.setDataType(dataGen.getDataType());
                transportJobBranch.setGenNum(dataGen.getGenNum());
                transportJobBranch.setTechType(dataGen.getTechType());
                transportJobBranch.setStationCount(dataGen.getStationCount());
                transportJobBranch.setCellCount(dataGen.getCellCount());
                transportJobBranch.setGmtCreate(new Date());
                transportJobBranch.setUserGuid(userGuid);
                transportJobBranch.setState("1");
                transportJobBranch.setAppCode(VerificationCode.codeGet(10));
//                transportJobBranch.setRegionCode(dataGen.getRegionCode());
                list.add(transportJobBranch);
            }
            transportJobBranchService.insertBatch(list);

            //判断大任务是否全为不变
            if (transportScheduleWebService.selectCountByJobGuidAndDataType(jobId, "4") == 0) {
                this.updateIsCompareByGuid(TransportJobStateConst.COMPLETE, Long.valueOf(jobId));
            } else {
                //入库结束,将transportJob表中任务状态改为待确认
                TransportJob transportJob = new TransportJob();
                transportJob.setGuid(jobId);
                transportJob.setIsCompare(TransportJobStateConst.COMPARE_SUCCESS);
                transportJobService.update(transportJob);
                confirmTransportJob(jobId);
            }
        }
        return null;
    }

    /**
     * 根据id查询
     */
    public TransportJobDTO findOneById(String id) {
        TransportJob transportJob = transportJobService.findById(id);
        TransportJobDTO transportJobDTO = new TransportJobDTO();
        BeanUtils.copyProperties(transportJob, transportJobDTO);
        return transportJobDTO;
    }

    /**
     * 更新数据
     *
     * @param btsIds   btsIds
     * @param jobId    jobId
     * @param dataType dataType
     */
    private void updateData(List<String> btsIds, String jobId, String regionCode, String dataType) {
        // 更新数据 设置 bts_data_type 状态
        if (btsIds.size() > 0) {
            int pageNum = btsIds.size() % 700 == 0 ? btsIds.size() / 700 : btsIds.size() / 700 + 1;
            System.out.println("总条数：" + btsIds.size() + "    总页数：" + pageNum + "    dataType：" + dataType);
            for (int i = 1; i <= pageNum; i++) {
                List<String> btsids;
                if (i == pageNum) {
                    btsids = btsIds.subList((i - 1) * 700, btsIds.size());
                } else {
                    btsids = btsIds.subList((i - 1) * 700, i * 700);
                }
                System.out.println("当前页：" + i + "    btsId条数：" + btsids.size());
                transportScheduleWebService.updateByBtsIds(dataType, jobId, btsids, regionCode);
                transportRawBtsDealWebService.updateByBtsIds(dataType, jobId, btsids, regionCode);
                transportRawBtsDealLogWebService.updateByBtsIds(dataType, jobId, btsids, regionCode);
            }
        }
    }

    /**
     * 任务确认受理
     */
    public JSONObject confirmTransportJob(String jobGuid) {
        TransportJob transportJob = transportJobService.findById(jobGuid);
        transportJob.setGmtModified(new Date());
        transportJob.setIsCompare(TransportJobStateConst.CONFIRM_PROCESSING);

        //写入任务申请通过和未审核通过数量
        int approvalCount = 0;
        List<TransportRawBtsDealLogDTO> transportRawBtsDealLogDTOList = transportRawBtsDealLogWebService.selectCountByUserGroupValid(jobGuid);
        for (TransportRawBtsDealLogDTO transportRawBtsDealLogDTO : transportRawBtsDealLogDTOList) {
            if (transportRawBtsDealLogDTO.getIsValid() == 1 || transportRawBtsDealLogDTO.getIsValid() == 3) {
                approvalCount += transportRawBtsDealLogDTO.getIsValidCount();
            } else if (transportRawBtsDealLogDTO.getIsValid() == 4) {
                transportJob.setDisapprovalCount(transportRawBtsDealLogDTO.getIsValidCount());
            }
        }
        transportJob.setApprovalCount(approvalCount);

        String jobSave = String.valueOf(transportJobService.save(transportJob));
        if (jobSave != null) {
            //自动确认
//            confirmCommitTransportJob(jobGuid);
            return JSONResult.getSuccessJson(transportJob, "无委确认受理成功");
        }
        return JSONResult.getFailureJson("无委确认受理失败");
    }

    /**
     * 3.0版本自动处理确认
     * */
//    public JSONObject confirmCommitTransportJob(String jobGuid){
//        TransportJob transportJob = transportJobService.findById(jobGuid);
//        transportJob.setGmtModified(new Date());
//        //修改确认数据状态 修改 小任务 为 CONFIRM_PASS 无委确认通过（待审核）
//        transportJobBranchWebService.updateByConfirm(jobGuid,TransportJobStateConst.CONFIRM_PASS);
//        if (transportJobBranchWebService.judgeAllConfirm(jobGuid) > 0){
//            //确认通过
//            transportJob.setIsCompare(TransportJobStateConst.CONFIRM_PASS);
//            //自动提交处理任务多线程
//            new Thread(new ProcessingRun(jobGuid,transportJob.getUserGuid())).start();
//            /*List<TransportJobBranchDTO> transportJobBranchDTOList = transportJobBranchWebService.findAllByJobState(jobGuid,"1");
//            if (transportJobBranchDTOList!= null && transportJobBranchDTOList.size() > 0){
//                //创建同步锁用于入库处理结束判断
//                int pageLock = transportJobBranchDTOList.size();
//                RedisLock redisLock = new RedisLock();
//                redisLock.setLock("1");
//                redisLock.setContent(pageLock);
//                //存入处理数量锁
//                String jobGuidSchedule = transportJobBranchDTOList.get(0).getJobGuid();
//                redisService.set(jobGuidSchedule+".approve." + "number",JSONObject.toJSONString(redisLock));
//
//                //创建同步锁用于入库操作结束判断
//                redisLock.setContent(0);
//                redisService.set(jobGuidSchedule+".approve.data." + "number",JSONObject.toJSONString(redisLock));
//
//                //处理提交任务
//                for (TransportJobBranchDTO transportJobBranchDTO : transportJobBranchDTOList){
//                    transportJobBranchWebService.commit(transportJob.getUserGuid(),transportJobBranchDTO.getGuid());
//                }
//            }*/
//        }else {
//            //确认不通过
//            transportJob.setIsCompare(TransportJobStateConst.CONFIRM_NOT_PASS);
//        }
//        String jobSave = String.valueOf(transportJobService.save(transportJob));
//        if (jobSave != null){
//            return JSONResult.getSuccessJson(transportJob,"无委确认受理成功");
//        }
//        return JSONResult.getFailureJson("无委确认受理失败");
//    }

    /**
     * 任务确认操作
     */
    public Map<String, Object> confirmCommitTransportJob(TransportJobConfirmVO transportJobConfirmVO, String token) {
        TransportJob transportJob = transportJobService.findById(transportJobConfirmVO.getJobId());
        transportJob.setGmtModified(new Date());
        if ("1".equals(transportJobConfirmVO.getIsConfirm())) {
            //修改确认数据状态
            transportJobBranchWebService.updateByConfirm(transportJobConfirmVO.getJobId(), transportJobConfirmVO.getDataType(), transportJobConfirmVO.getGenNum(), transportJobConfirmVO.getTechType(), TransportJobStateConst.CONFIRM_PASS);
            //修改deal表确认数据
            transportRawBtsDealWebService.updateConfirm(transportJobConfirmVO.getJobId(), transportJobConfirmVO.getDataType(), transportJobConfirmVO.getGenNum());
            //修改schedual表数据为已确认
            //transportScheduleWebService.updateConfirm(jobGuid,dataType,genNum);
            //判断此jobGuid下分支是否全部处理完成
            if (transportJobBranchWebService.judgeAllConfirm(transportJobConfirmVO.getJobId()) == 0) {
                //确认通过
                transportJob.setIsCompare(TransportJobStateConst.CONFIRM_PASS);
            }
        } else if ("2".equals(transportJobConfirmVO.getIsConfirm())) {

            //修改确认数据状态
            transportJobBranchWebService.updateByConfirm(transportJobConfirmVO.getJobId(), transportJobConfirmVO.getDataType(), transportJobConfirmVO.getGenNum(), transportJobConfirmVO.getTechType(), TransportJobStateConst.CONFIRM_NOT_PASS);
            //删除下载
            transportRawBtsDealWebService.deleteByJobIdDataTypeGenNum(transportJobConfirmVO.getJobId(), transportJobConfirmVO.getDataType(), transportJobConfirmVO.getGenNum());
            //删除待办
            transportScheduleWebService.deleteByJobIdDataTypeGenNum(transportJobConfirmVO.getJobId(), transportJobConfirmVO.getDataType(), transportJobConfirmVO.getGenNum());
            if (StringUtils.isNotEmpty(transportJobConfirmVO.getAppGuid())) {
                // 无委校验校验后不通过
                var atJob = approvalTransportJobWebService.findById(transportJobConfirmVO.getAppGuid());
                atJob.setIsCompare(ApprovalTransportJobStateConst.BRANCHIN_GIVEUP);
                atJob.setGmtModified(new Date());
                approvalTransportJobWebService.update(atJob);
                approvalRawBtsWebService.deleteByAppGuidDataTypeGenNum(transportJobConfirmVO.getAppGuid(), transportJobConfirmVO.getDataType(), transportJobConfirmVO.getGenNum());
            }
            if (transportJobBranchWebService.judgeAllConfirmFail(transportJobConfirmVO.getJobId()) == 0) {
                //确认不通过
                transportJob.setIsCompare(TransportJobStateConst.CONFIRM_NOT_PASS);
                try {
                    //删除消息通知
                    //restTemplate发送删除通知消息
                    myRestRepository.getForStringNpList(messageUrl + "deleteByFromGuid/" + transportJobConfirmVO.getJobId());
                } catch (Exception e) {

                }
            }
        }
        String jobSave = transportJobService.save(transportJob);
        if (jobSave != null) {
            return this.basicReturnSuccess(transportJob);
        }
        return this.basicReturnFailure("无委确认受理失败");
    }

    /**
     * 根据Guid修改流程状态
     */
    public int updateIsCompareByGuid(String isCompare, Long guid) {
        TransportJobNew transportJobNew = new TransportJobNew();
        transportJobNew.setId(guid);
        //transportJobNew.setIsCompare(isCompare);
        transportJobNew.setUpdatedAt(new Date());
        return transportJobNewService.update(transportJobNew);
    }

    /**
     * 修改
     */
    public int update(TransportJob transportJob) {
        return transportJobService.update(transportJob);
    }

    /**
     * 根据Id查询
     */
    public TransportJob findById(String guid) {
        return transportJobService.findById(guid);
    }

    /**
     * 根据Id查询
     */
    public List<TransportRawBtsDTO> findIncrease(String guid, String regionCode, String dataType) {
        return transportJobService.findIncrease(guid, regionCode, dataType);
    }

    public int updateIsDeleted(String jobId, String isDeal) {
        return transportJobService.updateIsDeleted(jobId, isDeal);
    }

    /**
     * 新增/变更数据对比及处理拉远站
     *
     * @param jobId   jobId
     * @param orgType orgType
     */
    public void checkExpandStation1(String jobId, String orgType,List<TransportSchedule> transportSchedules,UsersDTO usersDTO) {
//        //查出deal表中本批次数据中所有带拉远站标志的数据
//        List<TransportSchedule> transportRawBtsDealLY = transportScheduleWebService.findByJobId(jobId);
        //按bts_id进行分组
        Map<String, List<TransportSchedule>> btsGroup = transportSchedules.stream().collect(Collectors.groupingBy(TransportSchedule::getBtsId));
        //从分组中循环取出数据进行单独处理
        for (Map.Entry<String, List<TransportSchedule>> map : btsGroup.entrySet()) {
            String btsId = map.getKey();
            List<TransportSchedule> deals = map.getValue();
            //查询总表AsyncRawBts中相同btsId或拉远站
            List<AsyncRawBts> asyncRawBtsList = asyncRawBtsWebService.findByBtsId(btsId);
            if (asyncRawBtsList == null || asyncRawBtsList.isEmpty()) {
                //主表中没有，则代表本条数据为新增数据
                //首先判断deal表此btsId下基站有无拉远标识
                //拉远站处理
                //按拉远标识进行分组
                Map<String, List<TransportSchedule>> collect = deals.stream().collect(Collectors.groupingBy(TransportSchedule::getExpandStation));
                //判断带拉远标志的扇区是否为一个拉远站
                for (Map.Entry<String, List<TransportSchedule>> dealMap : collect.entrySet()) {
                    List<TransportSchedule> dealLYList = dealMap.getValue();
                    if ("2".equals(dealMap.getKey()) || "是".equals(dealMap.getKey())) {
                        //带拉远标识的新增
                        //先判断是否扇区为一个基站
                        compareExpandStation(dealLYList);
                    } else {
                        //不带拉远标识，直接循环添加新增标识
                        dealLYList.forEach(deal -> {
                            deal.setDataType("1");
                            deal.setBtsDataType("1");
                        });
                    }
                }
            } else {
                //将本批次数据和总表数据进行对比
                //判断deal同一btsid下是否存在拉远站标识
                boolean layuan = deals.stream().anyMatch(t -> t.getExpandStation().equals("2") || t.getExpandStation().equals("是"));
                if (layuan) {
                    //有拉远标识，进行拉远站处理
                    dealExpandStation(deals, asyncRawBtsList);
                } else {
                    //无拉远标识，直接进行新增变更比较
                    //使用cellId进行对应
                    for (TransportSchedule transportRawBtsDeal : deals) {
                        //查找有cellId对应的两条数据
                        AsyncRawBts asyncRawBts = asyncRawBtsList.stream().filter(t -> transportRawBtsDeal.getCellId().equals(t.getCellId())).findAny().orElse(null);
                        if (asyncRawBts == null) {
                            //如果AsyncRawBts没有对应的数据
                            //deal表中为新增cell
                            transportRawBtsDeal.setDataType("1");
                        } else {
                            if (CompareData.compareFilds(transportRawBtsDeal, asyncRawBts)) {
                                //不变
                                transportRawBtsDeal.setDataType("5");
                            } else {
                                //变更
                                transportRawBtsDeal.setDataType("2");
                            }
                        }
                    }
                    //设置bts_data_type
                    boolean isUpdate = deals.stream().anyMatch(t -> t.getDataType().equals("2"));
                    if (isUpdate) {
                        TransportSchedule transportRawBtsDeal = deals.stream().filter(t -> "2".equals(t.getDataType())).findAny().orElse(null);
                        for (TransportSchedule td : deals) {
                            td.setBtsName(transportRawBtsDeal.getBtsName());
                            td.setBtsId(transportRawBtsDeal.getBtsId());
                            td.setBtsDataType("2");
                        }
                    } else {
                        Map<String, List<TransportSchedule>> data = deals.stream().collect(Collectors.groupingBy(TransportSchedule::getDataType));
                        if (data.size() == 1) {
                            //说明全是新增或者全是不变
                            //获取key值
                            for (Map.Entry<String, List<TransportSchedule>> key : data.entrySet()) {
                                String dataType = key.getKey();
                                List<TransportSchedule> transportRawBtsDeals = key.getValue();
                                for (TransportSchedule td : transportRawBtsDeals) {
                                    td.setBtsDataType(dataType);
                                    if ("1".equals(dataType)) {
                                        for (TransportSchedule deal : key.getValue()) {
                                            deal.setBtsDataType("1");
                                        }
                                    } else {
                                        //全为不变
                                        for (TransportSchedule td1 : transportRawBtsDeals) {
                                            td1.setBtsDataType("5");
                                        }
                                    }
                                }
                            }
                        } else {
                            for (TransportSchedule td : deals) {
                                td.setBtsDataType("2");
                            }
                        }
                    }
                }
            }
        }
//        transportScheduleWebService.updateBatch(transportSchedules);

        //将数据按地区制式代数写入APPROVAL_TRANSPORT_JOB中
        if (!transportSchedules.isEmpty()) {
//            TransportJobNew transportJobNew = transportJobNewWebService.findById(Long.parseLong(jobId));
            //去除数据中bts_data_type为5的不变数据
            Map<TransportScheduleGroupDTO, List<TransportSchedule>> groupedPeople = transportSchedules.stream().filter(t ->!"5".equals(t.getBtsDataType()))
                    .collect(Collectors.groupingBy(transportSchedule ->
                            new TransportScheduleGroupDTO(transportSchedule.getBtsDataType(), transportSchedule.getGenNum(),transportSchedule.getTechType(),transportSchedule.getRegionCode())
                    ));
            List<ApprovalSchedule> list = new ArrayList<>();
            groupedPeople.forEach((key, value) -> {
                ApprovalTransportJob approvalTransportJob = new ApprovalTransportJob();
                approvalTransportJob.setJobId(Long.parseLong(jobId));
                approvalTransportJob.setIsCompare(TransportJobStateConst.COMPARE_SUCCESS);
                approvalTransportJob.setDataType(key.getBtsDataType());
                approvalTransportJob.setGenNum(key.getGenNum());
                approvalTransportJob.setTechType(key.getTechType());
                approvalTransportJob.setGmtCreate(new Date());
                approvalTransportJob.setUsersId(Long.parseLong(usersDTO.getUserId()));
                approvalTransportJob.setStatus("approving");
                approvalTransportJob.setOrgType(usersDTO.getType());
                approvalTransportJob.setAppCode(VerificationCode.codeGet(10));
                approvalTransportJob.setRegionCode(key.getRegionCode());
                approvalTransportJob.setRegionName(regionService.findOneByCode(key.getRegionCode()).getName());
                //计算基站数量
                approvalTransportJob.setStationCount(value.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TransportSchedule::getBtsId))),
                                ArrayList::new
                        )).size());



                Long approvalTransportJobId = approvalTransportJobWebService.save(approvalTransportJob);
                value.forEach(deal -> {
                    ApprovalSchedule approvalSchedule = new ApprovalSchedule();
                    BeanUtils.copyProperties(deal, approvalSchedule);
                    approvalSchedule.setAppId(approvalTransportJobId);

                    list.add(approvalSchedule);
                });
            });

            if (!list.isEmpty()){
                approvalScheduleWebService.insertBatchCreateId(list);
            }
        }

        //判断大任务是否全为不变
//        if (transportScheduleWebService.selectCountByJobGuidAndDataType(jobId, "4") == 0) {
//            transportJobProService.updateIsCompareByGuid(TransportJobStateConst.COMPLETE, jobId);
//        }
    }

    /**
     * 拉远站进行变更处理
     */
    private List<TransportSchedule> dealExpandStation(List<TransportSchedule> deals, List<AsyncRawBts> asyncRawBtsList) {
        for (TransportSchedule deal : deals) {
            //判断总表是否有此条扇区
            AsyncRawBts asyncRawBts = asyncRawBtsList.stream().filter(t -> deal.getCellId().equals(t.getCellId())).findAny().orElse(null);
            if (asyncRawBts == null) {
                //如果没有
                deal.setDataType("1");
            } else {
                if (CompareData.compareFilds(deal, asyncRawBts)) {
                    //不变
                    deal.setDataType("5");
                } else {
                    //变更
                    deal.setDataType("2");
                }
                //将deal中btsname和btsid修改为async中的同字段
                deal.setBtsId(asyncRawBts.getBtsId());
                deal.setBtsName(asyncRawBts.getBtsName());
            }
        }
        //先将deal list以拉远标识分组
        Map<String, List<TransportSchedule>> collect = deals.stream().collect(Collectors.groupingBy(TransportSchedule::getExpandStation));
        for (Map.Entry<String, List<TransportSchedule>> dealMap : collect.entrySet()) {
            if ("2".equals(dealMap.getKey()) || "是".equals(dealMap.getKey())) {
                //带拉远标识
                //按经纬度分成基站list
                // 按每个扇区的基本信息进行分组
                List<TransportSchedule> lyList = dealMap.getValue();
                Map<String, List<TransportSchedule>> lyMap = lyList.stream().collect(
                        Collectors.groupingBy(deal -> ListUtil.format("{0}{1}{2}{3}{4}{5}", deal.getLatitude() + deal.getTechType()
                                + deal.getLongitude() + deal.getAltitude() + deal.getLocation() + deal.getCounty())));
//                //将总表async list按btsId分成不同基站
//                Map<String, List<AsyncRawBts>> asyncMap = asyncRawBtsList.stream().collect(Collectors.groupingBy(AsyncRawBts::getBtsId));
                //拉远站之间进行对比，对比出btsDataType为新增还是变更
                for (Map.Entry<String, List<TransportSchedule>> dealList : lyMap.entrySet()) {
                    List<TransportSchedule> value = dealList.getValue();
                    boolean isUpdate = value.stream().anyMatch(t -> t.getDataType().equals("2"));
                    if (isUpdate) {
                        TransportSchedule transportRawBtsDeal = value.stream().filter(t -> "2".equals(t.getDataType())).findAny().orElse(null);
                        for (TransportSchedule td : value) {
                            td.setBtsName(transportRawBtsDeal.getBtsName());
                            td.setBtsId(transportRawBtsDeal.getBtsId());
                            td.setBtsDataType("2");
                        }
                    } else {
                        Map<String, List<TransportSchedule>> data = value.stream().collect(Collectors.groupingBy(TransportSchedule::getDataType));
                        if (data.size() == 1) {
                            //说明全是新增或者全是不变
                            //获取key值
                            for (Map.Entry<String, List<TransportSchedule>> key : data.entrySet()) {
                                String dataType = key.getKey();
                                List<TransportSchedule> transportRawBtsDeals = key.getValue();
                                for (TransportSchedule td : transportRawBtsDeals) {
                                    td.setBtsDataType(dataType);
                                    if ("1".equals(dataType)) {
                                        //全为新增
                                        String num = VerificationCode.codeGet(9);
                                        for (TransportSchedule deal : key.getValue()) {
                                            deal.setBtsId(deal.getBtsId() + "-" + num);
                                            deal.setBtsName(deal.getBtsName() + "(拉远站)");
                                            deal.setDataType("1");
                                            deal.setBtsDataType("1");
                                        }
                                    } else {
                                        //全为不变
                                        for (TransportSchedule td1 : transportRawBtsDeals) {
                                            td1.setBtsDataType("5");
                                        }
                                    }
                                }
                            }

                        } else {
                            for (TransportSchedule td : value) {
                                td.setBtsDataType("2");
                            }
                        }
                    }
                }
            }
        }
        return deals;
    }

    /**
     * 判断list中为几个拉远站
     */
    private List<TransportSchedule> compareExpandStation(List<TransportSchedule> lyList) {
        // 按每个扇区的基本信息进行分组
        Map<String, List<TransportSchedule>> dealMap = lyList.stream().collect(
                Collectors.groupingBy(deal -> ListUtil.format("{0}{1}{2}{3}{4}{5}{6}", deal.getLatitude() + deal.getTechType() + deal.getLongitude() + deal.getAltitude() + deal.getBtsName() + deal.getLocation() + deal.getCounty())));
        //循环修改拉远站信息
        for (Map.Entry<String, List<TransportSchedule>> map : dealMap.entrySet()) {
            String num = VerificationCode.codeGet(9);
            for (TransportSchedule deal : map.getValue()) {
                deal.setBtsId(deal.getBtsId() + "-" + num);
                deal.setBtsName(deal.getBtsName() + "(拉远站)");
                deal.setDataType("1");
                deal.setBtsDataType("1");
            }
        }
        return lyList;
    }

    /**
     * 延续/注销数据对比及处理拉远站
     *
     * @param jobId   jobId
     * @param orgType orgType
     */
    public void checkExpandStation2(String jobId, String orgType,List<TransportSchedule> transportSchedules,UsersDTO usersDTO) {
//        List<TransportSchedule> transportScheduleList = transportScheduleWebService.findByJobId(jobId);
        //将数据按地区制式代数写入APPROVAL_TRANSPORT_JOB中
        if (!transportSchedules.isEmpty()) {
//            TransportJobNew transportJobNew = transportJobNewWebService.findById(Long.parseLong(jobId));
            Map<TransportScheduleGroupDTO, List<TransportSchedule>> groupedPeople = transportSchedules.stream()
                    .collect(Collectors.groupingBy(transportSchedule ->
                            new TransportScheduleGroupDTO(transportSchedule.getBtsDataType(), transportSchedule.getGenNum(),transportSchedule.getTechType(),transportSchedule.getRegionCode())
                    ));
            List<ApprovalSchedule> list = new ArrayList<>();
            groupedPeople.forEach((key, value) -> {
                ApprovalTransportJob approvalTransportJob = new ApprovalTransportJob();
                approvalTransportJob.setJobId(Long.parseLong(jobId));
                approvalTransportJob.setIsCompare(TransportJobStateConst.COMPARE_SUCCESS);
                approvalTransportJob.setDataType(key.getBtsDataType());
                approvalTransportJob.setGenNum(key.getGenNum());
                approvalTransportJob.setTechType(key.getTechType());
                approvalTransportJob.setGmtCreate(new Date());
                approvalTransportJob.setUsersId(Long.parseLong(usersDTO.getUserId()));
                approvalTransportJob.setStatus("approving");
                approvalTransportJob.setAppCode(VerificationCode.codeGet(10));
                approvalTransportJob.setRegionCode(key.getRegionCode());
                Long approvalTransportJobId = approvalTransportJobWebService.save(approvalTransportJob);
                value.forEach(deal -> {
                    ApprovalSchedule approvalSchedule = new ApprovalSchedule();
                    BeanUtils.copyProperties(deal, approvalSchedule);
                    approvalSchedule.setAppId(approvalTransportJobId);

                    list.add(approvalSchedule);
                });
            });

            if (!list.isEmpty()) {
                approvalScheduleWebService.insertBatchCreateId(list);
            }
        }
    }

    /**
     * 当前已创建任务情况查询
     */
    public Map<String, Object> queryTransportJob(TransportJobVO vo, String token) {
        vo.setUserId(authWebService.findLoginUsersDTO(token).getUserId());
        List<TransportJobDTO> taskList = transportJobService.getTaskList(vo);
        if (taskList.size() == 0) {
            return this.basicReturnSuccess();
        }
        return this.basicReturnResultJson(new PageHandle(vo).buildPage(taskList));
    }

    /**
     * 选中任务详情查询
     */
    public Map<String, Object> getCurrentTaskOne(TransportJobVO dto, String token) {
        dto.setUserId(authWebService.findLoginUsersDTO(token).getUserId());

        TransportJobDTO transportJobModel = transportJobService.getCurrentTaskOne(dto);
        dto.setQueryFileType("file");
        List<TransportFileDTO> transportListForFile = transportJobService.getTaskFileList(dto);
        dto.setQueryFileType("material");
        List<TransportFileDTO> transportListForMaterial = transportJobService.getTaskFileList(dto);

        if (!transportListForFile.isEmpty()) {
            transportJobModel.setTransportFileDTOList(transportListForFile);
        } else {
            transportJobModel.setTransportFileDTOList(new ArrayList<>());
        }
        if (!transportListForMaterial.isEmpty()) {
            transportJobModel.setTransportFileAttachedDTOList(transportListForMaterial);
        } else {
            transportJobModel.setTransportFileAttachedDTOList(new ArrayList<>());
        }

        return this.basicReturnResultJson(transportJobModel);
    }

    /**
     * 删除任务
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> deleteTransportJob(TransportJobVO dto) {
        try {
            List<String> movIdList = transportJobService.getMovFileId(dto);
            for (String fileId : movIdList) {
                hdfsWebService.deleteFile(fileId);
            }
            //删除任务及相关联文件
            transportJobService.deleteTransportJob(dto);
            return this.basicReturnSuccess("已删除");
        } catch (Exception e) {
            e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return this.basicReturnFailure("删除失败");
        }
    }

    /**
     * 待比对任务查询
     */
    public Map<String, Object> queryTransportJobWaitComparison(TransportJobVO dto, String token) {
        dto.setUserId(authWebService.findLoginUsersDTO(token).getUserId());
        List<TransportJobDTO> comparisonJobList = transportJobService.getComparisonJob(dto);
        return this.basicReturnResultJson(new PageHandle(dto).buildPage(comparisonJobList));
    }

    /**
     * 待比任务结果详情查询
     */
    public Map<String, Object> queryTransportJobComparisonDetail(TransportJobVO dto) {
        Map<String, Object> map = new HashMap<>();
        TransportJob transportJob = transportJobService.findById(dto.getJobId());
        if (transportJob != null) {
            map.put("sumDataNum", transportJob.getCheckSuccessNum() + transportJob.getCheckFailedNum());
            List<transportDataDetailDTO> comparisonJobType = transportJobService.getComparisonJobType(dto);
            if (comparisonJobType.size() != 0) {
                map.put("data", comparisonJobType);
            }
        }
        return this.basicReturnResultJson(map);
    }

    /**
     * 获取待比任务结果状态选项
     */
    public Map<String, Object> getTransportJobStateList(String token) {
        return this.basicReturnSuccess(transportJobService.getTransportJobStateList());
    }

    /**
     * 待比任务各类结果下数据查询
     */
    public Map<String, Object> queryTransportJobDataDetail(TransportJobVO vo) {
        List<TransportRawBtsDealDTO> transportRawBtsDealDTOS = new ArrayList<>();
        if ("0".equals(vo.getDataType())) {
            //查询错误数据
            transportRawBtsDealDTOS = logTransportJobWebService.findByJobId(vo.getJobId());
        } else {
            //分类查看新增变更数据
            transportRawBtsDealDTOS = transportRawBtsDealLogWebService.findDealDataListR(vo);
        }
        if (transportRawBtsDealDTOS != null) {
            return this.basicReturnResultJson(new PageHandle(vo).buildPage(transportRawBtsDealDTOS));
        }
        return this.basicReturnSuccess();
    }
}
