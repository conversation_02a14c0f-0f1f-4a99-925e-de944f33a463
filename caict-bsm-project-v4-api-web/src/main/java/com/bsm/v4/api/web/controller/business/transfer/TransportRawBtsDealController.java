package com.bsm.v4.api.web.controller.business.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.TransportRawBtsDealWebService;
import com.bsm.v4.system.model.dto.business.transfer.TransportDealDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealDTO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: ycp
 * @createTime: 2023/08/17 15:39
 * @company: 成渝（成都）信息通信研究院
 * @description: 处理数据controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transfer/transportRawBtsDeal")
@Api(value = "web端数据处理管理接口", tags = "web端数据处理管理接口")
public class TransportRawBtsDealController extends BasicController {

    @Autowired
    private TransportRawBtsDealWebService transportRawBtsDealWebService;

    @ApiOperation(value = "查询各运营商各数据类型的待确认数量", notes = "查询各运营商各数据类型的待确认数量接口")
    @RequestMapping(value = "/findAllTransportRawBtsDealShowDTO", method = RequestMethod.GET)
    public JSONObject findAllTransportRawBtsDealShowDTO(){
        return this.basicReturnJson(TransportRawBtsDealWebService.class, TransportRawBtsDealWebService::findAllTransportRawBtsDealShowDTO);
    }

    @ApiOperation(value = "待确认数据列表查看", notes = "待确认数据列表查看接口")
    @RequestMapping(value = "/findDealDataList", method = RequestMethod.POST)
    public JSONObject findDealDataList(@RequestBody TransportDealDTO transportDealDTO){
        return this.basicReturnJson(transportDealDTO,TransportRawBtsDealWebService.class, (vo, service) -> service.findDealDataList(vo));
    }

    @ApiOperation(value = "下载交互数据", notes = "下载交互数据接口")
    @RequestMapping(value = "/exportExcel", method = RequestMethod.POST)
    public JSONObject exportExcel(@RequestBody(required = false) TransportRawBtsDealDTO transportRawBtsDealDTO, @RequestHeader("token")String token){
        return this.basicReturnJson(transportRawBtsDealDTO, TransportRawBtsDealWebService.class, (vo, service) -> service.exportExcelCq(token,vo));
//        transportRawBtsDealWebService.exportExcelCq(token,transportRawBtsDealDTO,response);
    }
}
