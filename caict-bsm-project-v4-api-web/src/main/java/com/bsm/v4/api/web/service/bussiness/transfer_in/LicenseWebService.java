package com.bsm.v4.api.web.service.bussiness.transfer_in;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.station.LicenseTWebService;
import com.bsm.v4.api.web.service.document.PdfExportWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.api.web.utils.CSVHelperUtil;
import com.bsm.v4.api.web.utils.DateUtils;
import com.bsm.v4.api.web.utils.ExcelHelperUtil;
import com.bsm.v4.domain.security.service.business.license.LicenseService;
import com.bsm.v4.domain.security.service.security.RegionService;
import com.bsm.v4.system.model.dto.business.license.LicenseDTO;
import com.bsm.v4.system.model.dto.business.license.LicensePDTO;
import com.bsm.v4.system.model.dto.business.license.LicensePdfDTO;
import com.bsm.v4.system.model.dto.business.license.LicenseStatisticDTO;
import com.bsm.v4.system.model.dto.business.station.ISectionDTO;
import com.bsm.v4.system.model.dto.security.RegionDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import com.bsm.v4.system.model.entity.business.license.RsbtLicenseT;
import com.bsm.v4.system.model.vo.business.License.LicenseVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.util.JSONResult;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Created by yanchengpeng on 2020/11/16.
 */
@Service
public class LicenseWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(LicenseWebService.class);
    @Autowired
    private LicenseService licenseService;
    //    @Autowired
//    private RsbtLicenseSyncService licenseSyncService;
    @Autowired
    private AuthWebService authWebService;
    @Autowired
    private RegionService regionService;
    @Autowired
    private LicenseTWebService licenseTWebService;
    @Autowired
    private PdfExportWebService pdfExportService;

    @Value("${caict.myFilePath}")
    private String myFilePath;
    @Value("${caict.generalTemplate}")
    private String generalTemplate;
    @Value("${caict.generalTemplateForm}")
    private String generalTemplateForm;
    @Value("${caict.myFileExportPath}")
    private String serverExportPath;

//    public String getLicenseDto( String stationGuid) {
//        LicensePdfDTO pdfDTO = licenseSyncService.findOnePdfDto(stationGuid);
//        pdfDTO.setStationCode("");
//        //表单数据
//        Map<String, String> map = new LinkedHashMap<>();
//        //二维码数据
//        Map<String, String> codeMap = new HashMap<String, String>();
//        //生成执照数据
//        this.setLicenseData(map, codeMap, pdfDTO);
//        String path = "";
//        if (pdfDTO != null) {
//            path = pdfExportService.savePdf(myFilePath, generalTemplate, pdfDTO.getStationGuid(), "template", map, codeMap);
//        }
//        return path;
//    }

    //使用台站名称作为名称
    private String getLicensePdfName(LicensePdfDTO pdfDTO, String type) {
        String pdfName = pdfDTO.getStationGuid();
        if ("print".equals(type)) {
            pdfName = pdfDTO.getStationGuid() + "print";
        }
        return pdfName;
    }

//    public void newPrintLicense(HttpServletResponse response, LicensePDTO licensePDTO, MyRestRepository myRestRepository, String url) {
//
//        LicensePdfDTO pdfDTO = licenseSyncService.findOnePdfDto(licensePDTO.getStationGuid());
//        if (pdfDTO != null) {
//            //表单数据
//            Map<String, String> map = new LinkedHashMap<>();
//            //二维码数据
//            Map<String, String> codeMap = new HashMap<String, String>();
//            //生成执照数据
//            this.setLicenseData(map, codeMap, pdfDTO);
//            String path = pdfExportService.savePdf(myFilePath, generalTemplateForm, pdfDTO.getStationGuid(), "template", map, codeMap);
//            path = myFilePath + path;
//            response.setContentType("application/pdf");
//            FileInputStream in;
//            OutputStream out;
//            try {
//                in = new FileInputStream(new File(path));
//                out = response.getOutputStream();
//                byte[] b = new byte[512];
//                while ((in.read(b)) != -1) {
//                    out.write(b);
//                }
//                out.flush();
//                in.close();
//                out.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//    }

    /**
     * 分页条件查询
     */
    public Map<String, Object> findAllByWhere(String token, LicenseVO vo) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        PageInfo<LicenseDTO> licenseDTOList;
//        String userType = usersDTO.getType();
//        if (userType.equals(UserRoleConst.USER_TYPE_MOBILE) || userType.equals(UserRoleConst.USER_TYPE_UNICOM) || userType.equals(UserRoleConst.USER_TYPE_TELECOM)) licenseDTO.setUserType(userType);
        if ("wuwei".equals(usersDTO.getType())) {
            // 无委
            licenseDTOList = new PageHandle(vo).buildPage(licenseService.findAllPageByWuWei(vo, usersDTO));
        } else if ("2".equals(usersDTO.getRoleDTO().getType())) {
            // 地市运营商
            licenseDTOList = new PageHandle(vo).buildPage(licenseService.findAllPageByWhere2(vo, usersDTO));
        } else {
            // 省级运营商
            licenseDTOList = new PageHandle(vo).buildPage(licenseService.findAllPageByWhere1(vo, usersDTO));
        }
        if (licenseDTOList.getList() != null) {
            return this.basicReturnSuccess(licenseDTOList);
        }
        return this.basicReturnFailure("操作失败");
    }

    /**
     * 编辑
     */
    public JSONObject saveLicense(LicenseDTO licenseDTO) {
        if (licenseDTO != null) {
            if (licenseDTO.getLicenseState().equals("5")) {
                licenseDTO.setLicenseState("1");
            }
            RsbtLicense bsmStationLicense = new RsbtLicense();
            RsbtLicenseT licenseT = new RsbtLicenseT();
            BeanUtils.copyProperties(licenseDTO, bsmStationLicense);
            // 换发，恢复
            if (licenseDTO.getLicenseState().equals("1")) {
                bsmStationLicense.setLicenseDateE(licenseDTO.getEndDate());
                bsmStationLicense.setLicenseDateB(licenseDTO.getStartDate());
            }
            BeanUtils.copyProperties(licenseDTO, licenseT);
            if (licenseService.save(bsmStationLicense) != null) {
                String guid = licenseTWebService.save(licenseT);
                if (guid != null) {
                    RsbtLicenseT licenseT1 = licenseTWebService.findOne(guid);
                    if (licenseTWebService.findOne(guid).getLicenseState() == 4)
//                        // todo
//                        myRestRepository.getForStringNpList(messageUrl + "deleteByFromGuid/" + licenseT1.getGuid());
                        return JSONResult.getSuccessJson(licenseDTO, "编辑成功");
                }
            }
            return JSONResult.getFailureJson("编辑失败");
        }
        return JSONResult.getFailureJson("未检测到参数");
    }

    /**
     * 执照数据生成
     * map;表单数据
     * codeMap：二维码数据
     * licensePdfDTO：数据源
     */
    private void setLicenseData(Map<String, String> map, Map<String, String> codeMap, LicensePdfDTO licensePdfDTO) {
        map.put("licenseCode", licensePdfDTO.getLicenseCode()); //执照编号
        codeMap.put("执照编号", licensePdfDTO.getLicenseCode()); //执照编号

        LocalDate licenseStartDate = licensePdfDTO.getLicenseStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();//有效期
        map.put("licenseStartDateYear", licenseStartDate.getYear() + "");
        map.put("licenseStartDateMonth", licenseStartDate.getMonthValue() + "");
        map.put("licenseStartDateDay", licenseStartDate.getDayOfMonth() + "");

        LocalDate licenseEndDate = licensePdfDTO.getLicenseEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();//到期时间
        map.put("licenseEndDateYear", licenseEndDate.getYear() + "");
        map.put("licenseEndDateMonth", licenseEndDate.getMonthValue() + "");
        map.put("licenseEndDateDay", licenseEndDate.getDayOfMonth() + "");

        map.put("stationName", licensePdfDTO.getStationName());                             //台站名称
        codeMap.put("基站名称", licensePdfDTO.getStationName());  //台站名称
        codeMap.put("基站地址", licensePdfDTO.getStatAddr());  //台站地址
//        map.put("stationCode", licensePdfDTO.getStationCode());                             //无线电识别码
        map.put("stationCode", "");                             //无线电识别码
        map.put("licensee", licensePdfDTO.getLicensee());                                //使用人
        map.put("licenseeNo", licensePdfDTO.getLicenseeNo());                              //统一社会信用代码
        codeMap.put("社会统一代码", licensePdfDTO.getLicenseeNo());       //社会统一代码
        map.put("licenseeCounty", licensePdfDTO.getStatAddr());                        //台址

        LocalDate localDate = LocalDate.now();
        map.put("isstueDate", localDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));   //颁发日期

        DecimalFormat df = new DecimalFormat("#.00");

        if (licensePdfDTO.getLongitude() != null) {
            int longitudeX = (int) Math.floor(Float.parseFloat(licensePdfDTO.getLongitude())); //度
            int longitudeY = (int) Math.floor((Float.parseFloat(licensePdfDTO.getLongitude()) - longitudeX) * 60); //分
            double number = (Double.parseDouble(licensePdfDTO.getLongitude()) - longitudeX) * 3600 - longitudeY * 60;
            String longitudeZ = new BigDecimal(number).setScale(1, BigDecimal.ROUND_HALF_UP).toString(); //秒
//                String[] strs = staPdfDTO.getLongitude().split("-");
            map.put("longitudeD", Integer.toString(longitudeX));//（经度）度
            map.put("longitudeF", Integer.toString(longitudeY));
            map.put("longitudeM", longitudeZ);
            codeMap.put("基站经度", licensePdfDTO.getLongitude());  //台站经度
        }

        if (licensePdfDTO.getLatitude() != null) {
            int latitudeX = (int) Math.floor(Float.parseFloat(licensePdfDTO.getLatitude())); //度
            int latitudeY = (int) Math.floor((Float.parseFloat(licensePdfDTO.getLatitude()) - latitudeX) * 60); //分
            double number1 = (Double.parseDouble(licensePdfDTO.getLatitude()) - latitudeX) * 3600 - latitudeY * 60;
            String latitudeZ = new BigDecimal(number1).setScale(1, BigDecimal.ROUND_HALF_UP).toString(); //秒
//                String[] strs = staPdfDTO.getLatitude().split("-");
            map.put("latitudeD", Integer.toString(latitudeX));//（纬度）度
            map.put("latitudeF", Integer.toString(latitudeY));
            map.put("latitudeM", latitudeZ);
            codeMap.put("基站经度", licensePdfDTO.getLongitude());  //台站纬度
        }

        //对应频率使用许可证编号
        map.put("licenseNo", licensePdfDTO.getLicenceNo());
//            for(LicenceNoEnum licenceNoEnum : LicenceNoEnum.values()){
//                if(licenceNoEnum.getName().equals(licensePdfDTO.getLicenceNo()) && licenceNoEnum.getType().equals(licensePdfDTO.getStationType())){
//                    map.put("licenseNo",licenceNoEnum.getValue());
//                }
//            }
        if (licensePdfDTO.getiSectionDTOList() != null && licensePdfDTO.getiSectionDTOList().size() != 0) {
            for (int i = 0; i < licensePdfDTO.getiSectionDTOList().size(); i++) {
                if (licensePdfDTO.getiSectionDTOList().get(i) != null) {
                    if (licensePdfDTO.getiSectionDTOList().get(i).getFreqEf() != null) {
                        String freqEf = licensePdfDTO.getiSectionDTOList().get(i).getFreqEf();
                        if (freqEf.contains(" kHz")) {
                            freqEf = freqEf.replace(" kHz", "");
                            double f = Double.parseDouble(freqEf);
                            if (f < 3000) {
                                freqEf += " kHz";
                            }
                            if (f > 3000 && f <= 3000000) {
                                freqEf = f / 1000 + " MHz";
                            }
                            if (f > 3000000 && f <= 3000000000D) {
                                freqEf = f / 1000000 + " GHz";
                            }
                        } else if (freqEf.contains(" MHz")) {
                            freqEf = freqEf.replace(" MHz", "");
                            double f = Double.parseDouble(freqEf);
                            if (f < 3) {
                                freqEf = f * 1000 + " kHz";
                            }
                            if (f > 3 && f <= 3000) {
                                freqEf += " MHz";
                            }
                            if (f > 3000 && f <= 3000000) {
                                freqEf = f / 1000 + " GHz";
                            }
                        } else if (freqEf.contains(" GHz")) {
                            freqEf = freqEf.replace(" GHz", "");
                            double f = Double.parseDouble(freqEf);
                            if (f < 0.003) {
                                freqEf = f * 1000 * 1000 + "kHz";
                            }
                            if (f > 0.003 && f <= 3) {
                                freqEf = f * 1000 + " MHz";
                            }
                            if (f > 3 && f <= 3000) {
                                freqEf += " GHz";
                            }
                        } else {
                            freqEf += " MHz";
                        }
                        map.put("freqEf" + (i + 1), freqEf);//发射频率
                    }

                    map.put("freqRf" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getFreqRf() + " MHz");//接收频率
                    if (licensePdfDTO.getiSectionDTOList().get(i).getEquPow() != null) {
                        map.put("equPow" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getEquPow() + " W");//发射功率
                    }
                    map.put("freqBand" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getFreqBand() + " MHz"); //必要带宽
                    map.put("freqOther" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getDetail());//其他必要信息

                    map.put("equAuth" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getEquAuth()); //发射设备型号核准代码
                    if (licensePdfDTO.getiSectionDTOList().get(i).getAntGain() != null) {
                        map.put("antGain" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getAntGain().toString() + " dBi");//天线增益
                    }
                    map.put("antPole" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getAntEpole());//极化方式
                    if (licensePdfDTO.getiSectionDTOList().get(i).getAntHight() != null) {
                        map.put("antHight" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getAntHight().toString() + " m");//天线距地高度
                    }
                }
            }
        }


        map.put("specificRequirements1", licensePdfDTO.getSpecialCase());//特别规定事项
        map.put("specificRequirements2", licensePdfDTO.getSpecialCase());//特别规定事项
    }

    public void setLicenseDataSta(Map<String, String> map, Map<String, String> codeMap, LicensePdfDTO licensePdfDTO) {
        map.put("licenseCode", licensePdfDTO.getLicenseCode()); //执照编号
        codeMap.put("执照编号", licensePdfDTO.getLicenseCode()); //执照编号

        LocalDate licenseStartDate = licensePdfDTO.getLicenseStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();//有效期
        map.put("licenseStartDateYear", licenseStartDate.getYear() + "");
        map.put("licenseStartDateMonth", licenseStartDate.getMonthValue() + "");
        map.put("licenseStartDateDay", licenseStartDate.getDayOfMonth() + "");

        LocalDate licenseEndDate = licensePdfDTO.getLicenseEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();//到期时间
        map.put("licenseEndDateYear", licenseEndDate.getYear() + "");
        map.put("licenseEndDateMonth", licenseEndDate.getMonthValue() + "");
        map.put("licenseEndDateDay", licenseEndDate.getDayOfMonth() + "");

        map.put("stationName", licensePdfDTO.getStationName());                             //台站名称
        codeMap.put("基站名称", licensePdfDTO.getStationName());  //台站名称
        codeMap.put("基站地址", licensePdfDTO.getStatAddr());  //台站地址
//        map.put("stationCode", licensePdfDTO.getStationCode());                             //无线电识别码
        map.put("stationCode", "");
        map.put("licensee", licensePdfDTO.getLicensee());                                //使用人
        map.put("licenseeNo", licensePdfDTO.getLicenseeNo());                              //统一社会信用代码
        codeMap.put("社会统一代码", licensePdfDTO.getLicenseeNo());       //社会统一代码
        map.put("licenseeCounty", licensePdfDTO.getStatAddr());                        //台址

        LocalDate localDate = LocalDate.now();
        map.put("isstueDate", localDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));   //颁发日期

        DecimalFormat df = new DecimalFormat("#.00");

        if (licensePdfDTO.getLongitude() != null) {
            int longitudeX = (int) Math.floor(Float.parseFloat(licensePdfDTO.getLongitude())); //度
            int longitudeY = (int) Math.floor((Float.parseFloat(licensePdfDTO.getLongitude()) - longitudeX) * 60); //分
            double number = (Double.parseDouble(licensePdfDTO.getLongitude()) - longitudeX) * 3600 - longitudeY * 60;
            String longitudeZ = new BigDecimal(number).setScale(1, BigDecimal.ROUND_HALF_UP).toString(); //秒
//                String[] strs = staPdfDTO.getLongitude().split("-");
            map.put("longitudeD", Integer.toString(longitudeX));//（经度）度
            map.put("longitudeF", Integer.toString(longitudeY));
            map.put("longitudeM", longitudeZ);
            codeMap.put("基站经度", licensePdfDTO.getLongitude());  //台站经度
        }

        if (licensePdfDTO.getLatitude() != null) {
            int latitudeX = (int) Math.floor(Float.parseFloat(licensePdfDTO.getLatitude())); //度
            int latitudeY = (int) Math.floor((Float.parseFloat(licensePdfDTO.getLatitude()) - latitudeX) * 60); //分
            double number1 = (Double.parseDouble(licensePdfDTO.getLatitude()) - latitudeX) * 3600 - latitudeY * 60;
            String latitudeZ = new BigDecimal(number1).setScale(1, BigDecimal.ROUND_HALF_UP).toString(); //秒
//                String[] strs = staPdfDTO.getLatitude().split("-");
            map.put("latitudeD", Integer.toString(latitudeX));//（纬度）度
            map.put("latitudeF", Integer.toString(latitudeY));
            map.put("latitudeM", latitudeZ);
            codeMap.put("基站经度", licensePdfDTO.getLongitude());  //台站纬度
        }

        //对应频率使用许可证编号
        map.put("licenseNo", licensePdfDTO.getLicenceNo());
//            for(LicenceNoEnum licenceNoEnum : LicenceNoEnum.values()){
//                if(licenceNoEnum.getName().equals(licensePdfDTO.getLicenceNo()) && licenceNoEnum.getType().equals(licensePdfDTO.getStationType())){
//                    map.put("licenseNo",licenceNoEnum.getValue());
//                }
//            }
        if (licensePdfDTO.getiSectionDTOList() != null && licensePdfDTO.getiSectionDTOList().size() != 0) {
            for (int i = 0; i < licensePdfDTO.getiSectionDTOList().size(); i++) {
                if (licensePdfDTO.getiSectionDTOList().get(i) != null) {
                    if (licensePdfDTO.getiSectionDTOList().get(i).getFreqEf() != null) {
                        String freqEf = licensePdfDTO.getiSectionDTOList().get(i).getFreqEf();
                        map.put("freqEf" + (i + 1), freqEf);//发射频率
                    }

                    map.put("freqRf" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getFreqRf());//接收频率
                    if (licensePdfDTO.getiSectionDTOList().get(i).getEquPow() != null) {
                        map.put("equPow" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getEquPow());//发射功率
                    }
                    map.put("freqBand" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getFreqBand()); //必要带宽
                    map.put("freqOther" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getDetail());//其他必要信息

                    map.put("equAuth" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getEquAuth()); //发射设备型号核准代码
                    if (licensePdfDTO.getiSectionDTOList().get(i).getAntGain() != null) {
                        map.put("antGain" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getAntGain().toString() + " dBi");//天线增益
                    }
                    map.put("antPole" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getAntEpole());//极化方式
                    if (licensePdfDTO.getiSectionDTOList().get(i).getAntHight() != null) {
                        map.put("antHight" + (i + 1), licensePdfDTO.getiSectionDTOList().get(i).getAntHight().toString() + " m");//天线距地高度
                    }
                }
            }
        }


        map.put("specificRequirements1", licensePdfDTO.getSpecialCase());//特别规定事项
        map.put("specificRequirements2", licensePdfDTO.getSpecialCase());//特别规定事项
    }

    /**
     * 执照打印
     */
    public JSONObject printLicense(String stationGuid, String type) {
        LicensePdfDTO licensePdfDTO = licenseService.findOneFDFByStationGuid(stationGuid);
        if (licensePdfDTO != null) {
            List<ISectionDTO> iSectionDTOList = licenseService.getISectionDTOList(stationGuid);
            if (iSectionDTOList != null && iSectionDTOList.size() != 0) {
                licensePdfDTO.setiSectionDTOList(iSectionDTOList);
            }
            String export = "export/";


            //表单数据
            Map<String, String> map = new LinkedHashMap<>();
            //二维码数据
            Map<String, String> codeMap = new HashMap<String, String>();
            //生成执照数据
            setLicenseData(map, codeMap, licensePdfDTO);
            if ("template".equals(type)) {
                return JSONResult.getSuccessJson(export + pdfExportService.savePdf(myFilePath + export, generalTemplate, stationGuid, type, map, codeMap), "执照生成成功");
            } else if ("print".equals(type)) {
                return JSONResult.getSuccessJson(export + pdfExportService.savePdf(myFilePath + export, generalTemplateForm, stationGuid + "print", type, map, codeMap), "执照生成成功");
            }
        }
        return JSONResult.getFailureJson("未检测到执照信息");
    }

    /**
     * 勾选下载
     */
    public JSONObject downloadLicenses(String[] stationGuid) {
        List<LicensePdfDTO> licensePdfDTOList = licenseService.findOneFDFByStationGuids(stationGuid);
        if (licensePdfDTOList != null && licensePdfDTOList.size() > 0) {
            List<String> filePathList = new ArrayList<>();
            String export = "export/";
            for (LicensePdfDTO licensePdfDTO : licensePdfDTOList) {
                licensePdfDTO.setStationCode("");
                //表单数据
                Map<String, String> map = new LinkedHashMap<>();
                //二维码数据
                Map<String, String> codeMap = new HashMap<>();
                //生成执照数据
                setLicenseData(map, codeMap, licensePdfDTO);

                String filePath = myFilePath + export + pdfExportService.savePdf(myFilePath + export, generalTemplate, licensePdfDTO.getStationGuid(), "template", map, codeMap);
                filePathList.add(filePath);
            }
            return JSONResult.getSuccessJson(export + pdfExportService.savePdfZip(myFilePath + export, filePathList), "勾选下载成功");
        }
        return JSONResult.getFailureJson("未检测到执照信息");
    }

    public void newPrintLicense(HttpServletResponse response, LicensePDTO licensePDTO) {
        JSONObject jsonObject = printLicense(licensePDTO.getStationGuid(), "print");
        if (jsonObject.get("data") != null) {
            String path = (String) jsonObject.get("data");
            path = myFilePath + path;
            response.setContentType("application/pdf");
            FileInputStream in;
            OutputStream out;
            try {
//                PdfReader reader = null;//PDF读取器
//                reader = new PdfReader(path);
                in = new FileInputStream(new File(path));
                out = response.getOutputStream();
                byte[] b = new byte[512];
                while ((in.read(b)) != -1) {
                    out.write(b);
                }
                out.flush();
                in.close();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取执照统计数据（已发、未发、过期、停用）
     *
     * @return json
     */
    public JSONObject getStatisticCount() {
        LicenseStatisticDTO statisticCount = licenseService.getStatisticCount();
        return JSONResult.getSuccessJson(statisticCount, "查询成功");
    }

    public int updateAppCode(String oldAppCode, String newAppCode) {
        return licenseService.updateAppCode(oldAppCode, newAppCode);
    }

    /**
     * 根据基站id查询执照
     */
    public RsbtLicense selectByStationGuid(String stationGuid) {
        return licenseService.selectByStationGuid(stationGuid);
    }

    public int updateBatch(List<RsbtLicense> licenseList) {
        return licenseService.updateBatch(licenseList);
    }

    /**
     * 根据地区下载数据
     *
     * @param licenseDTO licenseDTO
     * @return json
     */
    public JSONObject downloadLicensesByCounty(LicenseDTO licenseDTO) {
        if (StringUtils.isNotEmpty(licenseDTO.getLicenseCounty()) && StringUtils.isNotEmpty(licenseDTO.getOrgName())) {
            LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  根据地区下载数据--查询数据开始");
            List<LicensePdfDTO> licensePdfDTOList = licenseService.downloadLicensesByCounty(licenseDTO.getLicenseCounty(), licenseDTO.getOrgName());
            if (licensePdfDTOList != null && licensePdfDTOList.size() > 0) {
                LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  根据地区下载数据--查询数据完成");
                List<String> filePathList = new ArrayList<>();
                LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  根据地区下载数据--文件生成开始");

                long timestamp = System.currentTimeMillis();
                String export = "export/export_pdf/" + licenseDTO.getLicenseCounty() + timestamp + "/";
                File file = new File(myFilePath + export);
                if (!file.exists()) {
                    file.mkdirs();
                }
                // 生成Excel文件
                List<String[]> dataList = new ArrayList<>();
                dataList.add(getHead());
                dataList.addAll(getBody(licensePdfDTOList));
                String[][] datas = new String[dataList.size()][dataList.get(0).length];
                for (int i = 0; i < dataList.size(); i++) {
                    System.arraycopy(dataList.get(i), 0, datas[i], 0, dataList.get(0).length);
                }
                ExcelHelperUtil helper = new ExcelHelperUtil();
                String msg = helper.importDataToExcel(datas, myFilePath + export, licenseDTO.getLicenseCounty() + "_" + licenseDTO.getOrgName());
                // 生成的excel一起打包
                filePathList.add(msg.split("！")[1]);
                for (LicensePdfDTO licensePdfDTO : licensePdfDTOList) {
                    licensePdfDTO.setStationCode("");
                    //表单数据
                    Map<String, String> map = new LinkedHashMap<>();
                    //二维码数据
                    Map<String, String> codeMap = new HashMap<>();
                    //生成执照数据
                    setLicenseData(map, codeMap, licensePdfDTO);
                    String str;
                    if (licensePdfDTO.getLicensee().contains("联合网络")) {
                        str = "联通";
                    } else if (licensePdfDTO.getLicensee().contains("移动")) {
                        str = "移动";
                    } else if (licensePdfDTO.getLicensee().contains("电信")) {
                        str = "电信";
                    } else {
                        str = "广电";
                    }

                    String filePath = myFilePath + export + pdfExportService.savePdf(myFilePath + export, generalTemplate, str + "_" + licensePdfDTO.getStationGuid(), "template", map, codeMap);
                    filePathList.add(filePath);
                }
                LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  根据地区下载数据--文件生成完成");
                String path = "export/export_pdf/" + pdfExportService.savePdfZip1(myFilePath + "export/export_pdf/", licenseDTO.getOrgName() + "_" + licenseDTO.getLicenseCounty() + "_", filePathList);
                if (pdfExportService.deleteExcelByPath(new File(myFilePath + export))) {
                    LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  根据地区下载数据--文件删除成功");
                } else {
                    LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  根据地区下载数据--文件删除失败");
                }
                return JSONResult.getSuccessJson(path, "下载成功");
            } else {
                return JSONResult.getFailureJson("未检测到执照信息");
            }
        } else {
            return JSONResult.getFailureJson("请选择地区和运营商");
        }
    }

    /**
     * 获取地区信息
     *
     * @return json
     */
    public JSONObject getCounty(String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        String orgAreaCode = usersDTO.getOrgDTO().getOrgAreaCode();
        RegionDTO oneByCode = regionService.findOneByCode(orgAreaCode);
        return JSONResult.getSuccessJson(regionService.findAllByParentId(oneByCode.getId()));
    }


    /**
     * 获取excel数据
     *
     * @param licensePdfDTOList data list
     * @return List'<String[]>
     */
    private List<String[]> getBody(List<LicensePdfDTO> licensePdfDTOList) {
        List<String[]> list = new ArrayList<>();

        for (LicensePdfDTO dto : licensePdfDTOList) {
            String[] data = new String[8];
            data[0] = dto.getLicenseCode();
            data[1] = dto.getStationCode();
            data[2] = dto.getStationName();
            data[3] = dto.getStationType();
            data[4] = dto.getLongitude();
            data[5] = dto.getLatitude();
            data[6] = dto.getCountyName();
            data[7] = dto.getStatAddr();

            list.add(data);
        }
        return list;
    }

    /**
     * 获取excel表头
     *
     * @return String[]
     */
    private String[] getHead() {
        String[] head = new String[8];
        head[0] = "执照编号";
        head[1] = "基站识别码";
        head[2] = "基站名称";
        head[3] = "技术体制";
        head[4] = "经度";
        head[5] = "纬度";
        head[6] = "所属地区";
        head[7] = "台址";

        return head;
    }

    public Map<String, Object> exportExcel(String token, LicenseVO vo) {

        String timeStr = new Timestamp(System.currentTimeMillis()).toString().replaceAll("\\.", "_")
                .replace(" ", "_").replaceAll("-", "").replaceAll(":", "")
                .replaceAll("\\\\", "");
        // 打包后的文件路径
        String filePath = serverExportPath + "exportLicenseList" + File.separator + timeStr + File.separator;
        String fileName = timeStr;
        String resultPath = "export/exportLicenseList" + File.separator + timeStr + File.separator + fileName + ".csv";

        try {
            UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
            List<LicenseDTO> licenseDTOList;
            if ("wuwei".equals(usersDTO.getType())) {
                // 无委
                licenseDTOList = licenseService.findAllPageByWuWei(vo, usersDTO);
            } else if ("2".equals(usersDTO.getRoleDTO().getType())) {
                // 地市运营商
                licenseDTOList = licenseService.findAllPageByWhere2(vo, usersDTO);
            } else {
                // 省级运营商
                licenseDTOList = licenseService.findAllPageByWhere1(vo, usersDTO);
            }
            if (licenseDTOList != null) {
                List<List<Object>> list = new ArrayList<>();
                list.add(Arrays.asList("申请表编号","设台单位","执照编号","基站名称","基站识别码","有效期起","到期时间","执照状态","基站地址","制式"));
                for (LicenseDTO license : licenseDTOList) {
                    List<Object> listTemp = new ArrayList<>();
                    listTemp.add(license.getApplyTableCode());
                    listTemp.add(license.getOrgName());
                    listTemp.add(license.getLicenseCode());
                    listTemp.add(license.getStationName());
                    listTemp.add(license.getStationCode());
                    if(license.getLicenseDateB() != null){
                        listTemp.add(DateUtils.formatDate(license.getLicenseDateB(), "yyyy-MM-dd"));
                    }else{
                        listTemp.add("");
                    }
                    if(license.getLicenseDateE() != null){
                        listTemp.add(DateUtils.formatDate(license.getLicenseDateE(), "yyyy-MM-dd"));
                    }else{
                        listTemp.add("");
                    }
                    listTemp.add(getLicenseStateValue(Integer.parseInt(license.getLicenseState())));
                    listTemp.add(license.getLocation());
                    listTemp.add(license.getNetType());
                    list.add(listTemp);
                }
                if(CSVHelperUtil.createCSVFileUrl(list, filePath, fileName, serverExportPath + "exportLicenseList")){
                    return this.basicReturnSuccess(resultPath);
                }else{
                    return this.basicReturnFailure("导出数据失败！");
                }
            } else {
                return this.basicReturnFailure("导出数据为空！");
            }
        } catch (Exception e) {
            return this.basicReturnFailure("文件导出失败：" + e.getMessage());
        }
    }

    //执照状态map
    private static final Map<Integer, String> LICENSE_STATE_MAP = Map.of(
            1, "已发",
            2, "过期",
            3, "停用",
            4, "注销",
            5, "即将过期"
    );

    /*
      * 获取Licence状态值
    */
    public String getLicenseStateValue(int code) {
        return LICENSE_STATE_MAP.getOrDefault(code, "未知");
    }
}
