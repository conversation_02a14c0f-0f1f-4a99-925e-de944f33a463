package com.bsm.v4.api.web.controller.business.applytable;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.applytable.RsbtApplyWebService;
import com.bsm.v4.system.model.dto.business.applytable.AddApplyDTO;
import com.bsm.v4.system.model.dto.business.applytable.RsbtApplyDTO;
import com.bsm.v4.system.model.vo.business.applytable.ApplyTableVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by dengsy on 2020-05-08.
 */
@RestController
@RequestMapping(value = "/apiWeb/applytable/bsmApplyTable")
@Api(value = "web端申请表接口", tags = "web端申请表接口")
public class RsbtApplyController extends BasicController {

    @Autowired
    private RsbtApplyWebService bsmApplyTableWebService;

    @ApiOperation(value = "分页条件查询", notes = "分页条件查询接口")
    @RequestMapping(value = "/findAllByWhere", method = RequestMethod.POST)
    public JSONObject findAllByWhere(@RequestBody RsbtApplyDTO vo){
        return this.basicReturnJson(vo, RsbtApplyWebService.class, (param, service) -> service.findAllByWhere(vo));
    }

    @ApiOperation(value = "分页条件查询申请表列表", notes = "分页条件查询申请表列表接口")
    @RequestMapping(value = "/findApplyList", method = RequestMethod.POST)
    public JSONObject findApplyList(@RequestBody ApplyTableVO vo,@RequestHeader("token") String token){
        return this.basicReturnJson(vo, RsbtApplyWebService.class, (param, service) -> service.findApplyList(vo,token));
    }

    @ApiOperation(value = "分页条件查询申请表详情列表", notes = "分页条件查询申请表详情列表接口")
    @RequestMapping(value = "/findApplyDetailList/{jobId}", method = RequestMethod.GET)
    public JSONObject findApplyDetailList(@PathVariable("jobId")String jobId){
        return this.basicReturnJson(jobId, RsbtApplyWebService.class, (param, service) -> service.findApplyDetailList(jobId));
    }

    @ApiOperation(value = "申请表详情", notes = "申请表详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "guid",value = "guid",paramType = "path",dataType = "String")
    })
    @RequestMapping(value = "/findOneByGuid/{guid}", method = RequestMethod.GET)
    public JSONObject findOneByGuid(@PathVariable("guid") String guid){
        return bsmApplyTableWebService.findOneByGuid(guid);
    }

    @ApiOperation(value = "内网端分页条件查询", notes = "内网端分页条件查询接口")
    @RequestMapping(value = "/findAllInByWhere", method = RequestMethod.POST)
    public JSONObject findAllInByWhere(@RequestBody RsbtApplyDTO bsmApplytableDTO, @RequestHeader("token") String token){
        return bsmApplyTableWebService.findAllInByWhere(bsmApplytableDTO,token);
    }

    @ApiOperation(value = "内网端申请表详情查询", notes = "内网端申请表详情查询接口")
    @RequestMapping(value = "/findTechTable/{guid}", method = RequestMethod.GET)
    public JSONObject findApplyDetail(@PathVariable("guid") String guid){
        return bsmApplyTableWebService.findApplyDetail(guid);
    }

    @ApiOperation(value = "内网端技术资料表查询", notes = "内网端技术资料表查询接口")
    @RequestMapping(value = "/findTechTable", method = RequestMethod.POST)
    public JSONObject findTechTable(@RequestBody RsbtApplyDTO applyDTO){
        return bsmApplyTableWebService.findTechTable(applyDTO);
    }

    /**
     * 编辑申请表
     */
    @ApiOperation(value = "内网端编辑申请表保存", notes = "内网端编辑申请表保存接口")
    @RequestMapping(value = "/editApply", method = RequestMethod.POST)
    public JSONObject editApply(@RequestBody AddApplyDTO addApplyDTO){
        return bsmApplyTableWebService.editApply(addApplyDTO);
    }

    @ApiOperation(value = "申请表打印", notes = "申请表打印接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "guid",value = "申请表主键",paramType = "path",dataType = "String")
    })
    @RequestMapping(value = "/printPdf/{guid}", method = RequestMethod.GET)
    public JSONObject printPdf( @PathVariable("guid")String guid){
        return this.basicReturnJson(guid, RsbtApplyWebService.class, (param, service) -> service.printPdf(guid));
    }
}
