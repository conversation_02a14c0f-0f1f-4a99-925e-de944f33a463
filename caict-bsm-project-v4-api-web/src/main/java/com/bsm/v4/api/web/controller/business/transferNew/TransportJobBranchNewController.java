package com.bsm.v4.api.web.controller.business.transferNew;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.ApprovalTransportJobWebService;
import com.bsm.v4.api.web.service.bussiness.transferNew.TransportJobBranchNewWebService;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 按地市拆分的子任务表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/zhuanTransportJobBranch")
@Api(value = "web端按地市拆分的子任务管理接口", tags = "按地市拆分的子任务管理接口")
public class TransportJobBranchNewController extends BasicController {
    
}
