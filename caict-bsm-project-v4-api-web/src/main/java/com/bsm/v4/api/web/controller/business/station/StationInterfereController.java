package com.bsm.v4.api.web.controller.business.station;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.station.StationInterfereWebService;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobDTO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping(value = "/apiWeb/station/interfere")
@Api(value = "web端5G干扰协调接口", tags = "web端5G干扰协调接口")
public class StationInterfereController extends BasicController {

    @Autowired
    private StationInterfereWebService stationInterfereWebService;

//    @ApiOperation(value = "查看需5G干扰协调的任务接口", notes = "查看需5G干扰协调的任务接口")
//    @RequestMapping(value = "/findListByPage", method = RequestMethod.POST)
//    public JSONObject findListByPage(@RequestBody TransportJobDTO transportJobDTO, HttpServletRequest request) {
//        Object accessToken = request.getHeader("token");
//        transportJobDTO.setToken(accessToken.toString());
//        return this.basicReturnJson(transportJobDTO, StationInterfereWebService.class, (param, service) -> service.findListByPage(transportJobDTO));
//    }
//
//    @ApiOperation(value = "开始协调接口", notes = "开始协调的任务接口")
//    @RequestMapping(value = "/startInterfere/{guid}/{appGuid}", method = RequestMethod.GET)
//    public JSONObject startInterfere(@PathVariable("guid") String guid, @PathVariable("appGuid") String appGuid) {
//        return this.basicReturnJson(guid, appGuid, StationInterfereWebService.class, (param, service) -> service.getAppropriateList(guid, appGuid));
//    }
//
//    @ApiOperation(value = "查看5G干扰协调结果列表接口", notes = "查看5G干扰协调结果列表接口")
//    @RequestMapping(value = "/findResultList", method = RequestMethod.POST)
//    public JSONObject findResultList(@RequestBody ApprovalScheduleLogDTO approvalScheduleLogDTO) {
//        return this.basicReturnJson(approvalScheduleLogDTO, StationInterfereWebService.class, (param, service) -> service.findResultList(approvalScheduleLogDTO));
//    }
}
