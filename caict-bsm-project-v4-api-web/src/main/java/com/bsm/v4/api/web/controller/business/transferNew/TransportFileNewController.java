package com.bsm.v4.api.web.controller.business.transferNew;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.document.HdfsWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.domain.security.service.business.transfer.TransportFileService;
import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportFile;
import com.bsm.v4.system.model.vo.business.transferNew.FileToUploadVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 传输文件表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/transportFile")
@Api(value = "传输文件管理接口", tags = "传输文件管理接口")
public class TransportFileNewController extends BasicController {

    @Autowired
    private AuthWebService authWebService;

    @Autowired
    private HdfsWebService hdfsWebService;

    @Autowired
    private TransportFileService transportFileService;

    @ApiOperation(value = "上传文件", notes = "支持上传CSV文件和材料附件，自动校验文件类型和大小。现在使用统一的HDFS文件管理")
    @PostMapping(value = "/upload")
    public JSONObject uploadFile(
            @ApiParam(value = "上传的文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "文件上传参数", required = true) @ModelAttribute FileToUploadVO fileToUploadVO,
            @ApiParam(value = "用户token", required = true) @RequestHeader("token") String token) {
        
        try {
            // 使用统一的HDFS文件上传服务
            Map<String, Object> result = hdfsWebService.hdfsUpload(file, fileToUploadVO.getFileType(), fileToUploadVO, token);
            
            // 构建返回结果
            JSONObject response = new JSONObject();
            if ((Boolean) result.get("success")) {
                response.put("success", true);
                response.put("message", "文件上传成功");
                response.put("data", result.get("data"));
            } else {
                response.put("success", false);
                response.put("message", result.get("message"));
            }
            
            return response;
            
        } catch (Exception e) {
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("message", "文件上传失败：" + e.getMessage());
            return errorResponse;
        }
    }

    @ApiOperation(value = "根据任务ID查询文件列表", notes = "查询指定任务下的所有文件（使用统一TRANSPORT_FILE表）")
    @GetMapping(value = "/findByJobId")
    public JSONObject findByJobId(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId) {
        
        try {
            List<TransportFile> fileList = transportFileService.findByJobId(jobId);
            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", fileList);
            return response;
        } catch (Exception e) {
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("message", "查询失败：" + e.getMessage());
            return errorResponse;
        }
    }

/*    @ApiOperation(value = "根据任务ID和文件类型查询文件列表", notes = "查询指定任务下指定类型的文件")
    @GetMapping(value = "/findByJobIdAndFileType")
    public JSONObject findByJobIdAndFileType(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId,
            @ApiParam(value = "文件类型", required = true) @RequestParam("fileType") String fileType) {
        
        return this.basicReturnJson(jobId, TransportFileNewWebService.class,
                (id, service) -> service.findByJobIdAndFileType(id, fileType));
    }*/

/*
    @ApiOperation(value = "分页查询文件列表", notes = "支持多条件分页查询文件列表")
    @PostMapping(value = "/findPageWithConditions")
    public JSONObject findPageWithConditions(
            @ApiParam(value = "查询条件", required = true) @RequestBody TransportFileNewDTO dto) {
        
        return this.basicReturnJson(dto, TransportFileNewWebService.class,
                (condition, service) -> service.findPageWithConditions(condition));
    }
*/

/*
    @ApiOperation(value = "更新文件状态", notes = "更新文件的处理状态")
    @PutMapping(value = "/updateFileState")
    public JSONObject updateFileState(
            @ApiParam(value = "文件ID", required = true) @RequestParam("id") Long id,
            @ApiParam(value = "新状态（0-创建，1-上传成功，2-处理完成，3-处理异常）", required = true) @RequestParam("fileState") Integer fileState) {
        
        return this.basicReturnJson(id, TransportFileNewWebService.class,
                (fileId, service) -> service.updateFileState(fileId, fileState));
    }
*/

    @ApiOperation(value = "删除文件", notes = "软删除文件记录（使用统一TRANSPORT_FILE表）。限制：不能删除已处理的文件")
    @DeleteMapping(value = "/delete")
    public JSONObject deleteFile(
            @ApiParam(value = "文件ID", required = true) @RequestParam("id") Long id) {
        
        try {
            TransportFile file = transportFileService.findById(String.valueOf(id));
            if (file == null) {
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("success", false);
                errorResponse.put("message", "文件不存在");
                return errorResponse;
            }

            // 检查文件状态，不能删除已处理的文件
            if (file.getStatus() != null && (file.getStatus() == 2 || file.getStatus() == 3)) {
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("success", false);
                errorResponse.put("message", "不能删除已处理的文件");
                return errorResponse;
            }

            // 软删除：设置is_deleted标志
            file.setIsDeleted(1);
            file.setGmtModified(new Date());
            transportFileService.update(file);

            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("message", "文件删除成功");
            return response;
            
        } catch (Exception e) {
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("message", "删除失败：" + e.getMessage());
            return errorResponse;
        }
    }

    @ApiOperation(value = "统计任务文件", notes = "统计指定任务下各类型文件的数量（使用统一TRANSPORT_FILE表）")
    @GetMapping(value = "/countByJobIdGroupByFileType")
    public JSONObject countByJobIdGroupByFileType(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId) {
        
        try {
            List<TransportFileDTO> statisticsList = transportFileService.countByJobIdGroupByFileType(jobId);
            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("message", "统计成功");
            response.put("data", statisticsList);
            return response;
        } catch (Exception e) {
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("message", "统计失败：" + e.getMessage());
            return errorResponse;
        }
    }
}
