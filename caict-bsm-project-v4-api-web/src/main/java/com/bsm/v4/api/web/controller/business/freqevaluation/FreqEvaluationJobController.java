package com.bsm.v4.api.web.controller.business.freqevaluation;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.freqevaluation.FreqEvaluationJobWebService;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationJobVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价job
 * @date 2023年8月24日 11点13分
 */
@RestController
@RequestMapping(value = "/apiWeb/freq/evaluationJob")
@Api(value = "web端频率使用率评价job接口", tags = "web端频率使用率评价job接口")
public class FreqEvaluationJobController extends BasicController {


    @ApiOperation(value = "添加、修改使用率评价job", notes = "添加、修改使用率评价job接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "freqEvaluationJobVO", value = "使用率评价job对象", required = true, paramType = "body", dataType = "freqEvaluationJobVO")
    })
    @PostMapping(value = "`")
    public JSONObject save(@RequestBody FreqEvaluationJobVO freqEvaluationJobVO) {
        return this.basicReturnJson(freqEvaluationJobVO, FreqEvaluationJobWebService.class, (vo, service) -> service.save(vo));
    }

    @ApiOperation(value = "删除使用率评价job", notes = "删除使用率评价job接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "使用率评价jobid", required = true, dataType = "String")
    })
    @DeleteMapping(value = "/{id}")
    public JSONObject delete(@PathVariable("id") String id) {
        return this.basicReturnJson(id, FreqEvaluationJobWebService.class, (vo, service) -> service.delete(vo));
    }

    @ApiOperation(value = "分页条件查询", notes = "分页条件查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "freqEvaluationJobVO", value = "使用率评价job搜索对象", required = true, paramType = "body", dataType = "freqEvaluationJobVO")
    })
    @PostMapping(value = "/findAllByWhere")
    public JSONObject findAllByWhere(@RequestBody FreqEvaluationJobVO freqEvaluationJobVO) {
        return this.basicReturnJson(freqEvaluationJobVO, FreqEvaluationJobWebService.class, (vo, service) -> service.findAllByWhere(vo));
    }

}
