package com.bsm.v4.api.web.controller.business.license;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer_in.LicenseWebService;
import com.bsm.v4.system.model.vo.business.License.LicenseVO;
import com.bsm.v4.api.web.controller.config.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/2 17:19
 */

@RestController
@RequestMapping(value = "/apiWeb/license/rsbtStation")
@Api(value = "web端执照数据管理接口", tags = "web端执照数据管理接口")
public class LicenseController extends BasicController {
    @Autowired
    private LicenseWebService licenseWebService;

    @ApiOperation(value = "分页条件查询", notes = "分页条件查询接口")
    @RequestMapping(value = "/findAllByWhere", method = RequestMethod.POST)
    public JSONObject findAllByWhere(@RequestBody LicenseVO vo, @RequestHeader("token")String token){
        return this.basicReturnJson(vo, LicenseWebService.class, (param, service) -> licenseWebService.findAllByWhere(token,vo));
    }

    /**
     * 导出查询数据
     *
     * @param vo vo
     * @return json
     */
    @RequestMapping(value = "/exportExcel", method = RequestMethod.POST)
    public JSONObject exportExcel(@RequestBody LicenseVO vo, @RequestHeader("token")String token) {
        return this.basicReturnJson(vo, LicenseWebService.class, (LicenseVO, service) -> service.exportExcel(token,vo));

    }

}
