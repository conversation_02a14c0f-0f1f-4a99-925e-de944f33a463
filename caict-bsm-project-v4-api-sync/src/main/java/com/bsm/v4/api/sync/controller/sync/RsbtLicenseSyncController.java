package com.bsm.v4.api.sync.controller.sync;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.sync.business.sync.transfer.RsbtLicenseSyncService;
import com.bsm.v4.system.model.dto.business.license.RsbtLicensePdfDTO;
import com.bsm.v4.api.web.controller.config.BasicController;
import com.caictframework.utils.util.JSONResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/apiSync/standard/license")
@Api(value = "sync执照信息接口", tags = "sync执照信息接口")
public class RsbtLicenseSyncController extends BasicController {

    @Autowired
    private RsbtLicenseSyncService webService;


    @ApiOperation(value = "电子执照数据查询总数", notes = "电子执照数据查询总数接口")
    @RequestMapping(value = "/selectLicensePdfDto", method = RequestMethod.POST)
    public JSONObject selectLicensePdfDto(@RequestBody RsbtLicensePdfDTO rsbtLicensePdfDTO) {
        return JSONResult.getSuccessJson(webService.selectLicensePdfDto(rsbtLicensePdfDTO.getLinceseCode(),
                rsbtLicensePdfDTO.getStationCode(), rsbtLicensePdfDTO.getStaName(), rsbtLicensePdfDTO.getLinceseStartDate(), rsbtLicensePdfDTO.getLinceseEndDate()));
    }

    @ApiOperation(value = "电子执照数据查询", notes = "电子执照数据查询接口")
    @RequestMapping(value = "/findAllPdfDto", method = RequestMethod.POST)
    public JSONObject findAllPdfDto(@RequestBody RsbtLicensePdfDTO rsbtLicensePdfDTO) {
        return JSONResult.getSuccessJson(webService.findAllPdfDto(rsbtLicensePdfDTO.getLinceseCode(),
                rsbtLicensePdfDTO.getStationCode(), rsbtLicensePdfDTO.getStaName(), rsbtLicensePdfDTO.getLinceseStartDate(),
                rsbtLicensePdfDTO.getLinceseEndDate(), rsbtLicensePdfDTO.getPage(), rsbtLicensePdfDTO.getRows()));
    }

    @ApiOperation(value = "查询单个执照信息", notes = "查询单个执照信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stationGuid", value = "基站guid", required = true, paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/findOnePdfDto/{stationGuid}", method = RequestMethod.GET)
    public JSONObject findOnePdfDto(@PathVariable("stationGuid") String stationGuid) {
        return JSONResult.getSuccessJson(webService.findOnePdfDto(stationGuid));
    }
}
