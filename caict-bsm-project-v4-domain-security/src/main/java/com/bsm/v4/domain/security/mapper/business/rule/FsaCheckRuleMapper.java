package com.bsm.v4.domain.security.mapper.business.rule;


import com.bsm.v4.system.model.entity.business.rule.FsaCheckRule;
import com.bsm.v4.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.bsm.v4.system.model.vo.business.rule.FsaCheckRuleVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface FsaCheckRuleMapper extends BasicMapper<FsaCheckRule> {

    /**
     * 条件查询总数
     */
    @Select("select count(*) from FSA_CHECK_RULE,RSBT_ORG " +
            "where FSA_CHECK_RULE.ORG_GUID = RSBT_ORG.ORG_GUID " +
            "and (RSBT_ORG.TYPE = #{fsaCheckRuleVO.orgName,jdbcType=VARCHAR} or #{fsaCheckRuleVO.orgName,jdbcType=VARCHAR} is null) " +
            "and (FSA_CHECK_RULE.NET_TS = #{fsaCheckRuleVO.netTs,jdbcType=VARCHAR} or #{fsaCheckRuleVO.netTs,jdbcType=VARCHAR} is null) " +
            "and (FSA_CHECK_RULE.GEN_NUM = #{fsaCheckRuleVO.genNum,jdbcType=VARCHAR} or #{fsaCheckRuleVO.genNum,jdbcType=VARCHAR} is null) " +
            "and (FSA_CHECK_RULE.FILE_NO like concat(concat('%',#{fsaCheckRuleVO.fileNo,jdbcType=VARCHAR}),'%') or #{fsaCheckRuleVO.fileNo,jdbcType=VARCHAR} is null) " +
            "and (FSA_CHECK_RULE.STATE = #{fsaCheckRuleVO.state,jdbcType=VARCHAR} or #{fsaCheckRuleVO.state,jdbcType=VARCHAR} is null) " +
            "and (RSBT_ORG.ORG_USER = #{userId,jdbcType=VARCHAR} or #{userId,jdbcType=VARCHAR} is null) ")
    int selectCountByWhere(@Param("fsaCheckRuleVO") FsaCheckRuleVO fsaCheckRuleVO, @Param("userId") String userId);

    /**
     * 分页条件查询
     */
    @Select("select FSA_CHECK_RULE.*,RSBT_ORG.ORG_NAME,RSBT_ORG_APPENDIX.TYPE as orgType from FSA_CHECK_RULE,RSBT_ORG,RSBT_ORG_APPENDIX " +
            "where FSA_CHECK_RULE.ORG_GUID = RSBT_ORG.GUID AND RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "and (FSA_CHECK_RULE.NET_TS = #{fsaCheckRuleVO.netTs,jdbcType=VARCHAR} or #{fsaCheckRuleVO.netTs,jdbcType=VARCHAR} is null) " +
            "and (FSA_CHECK_RULE.GEN_NUM = #{fsaCheckRuleVO.genNum,jdbcType=VARCHAR} or #{fsaCheckRuleVO.genNum,jdbcType=VARCHAR} is null) " +
            "and (FSA_CHECK_RULE.FILE_NO like concat(concat('%',#{fsaCheckRuleVO.fileNo,jdbcType=VARCHAR}),'%') or #{fsaCheckRuleVO.fileNo,jdbcType=VARCHAR} is null) " +
            "and (FSA_CHECK_RULE.STATE = #{fsaCheckRuleVO.state,jdbcType=VARCHAR} or #{fsaCheckRuleVO.state,jdbcType=VARCHAR} is null) " +
            "and (FSA_CHECK_RULE.ORG_TYPE = #{fsaCheckRuleVO.orgType,jdbcType=VARCHAR} or #{fsaCheckRuleVO.orgType,jdbcType=VARCHAR} is null) " +
            "order by RSBT_ORG_APPENDIX.TYPE,FSA_CHECK_RULE.GEN_NUM,FSA_CHECK_RULE.NET_TS ")
    List<FsaCheckRuleDTO> findAllPageByWhere(@Param("fsaCheckRuleVO") FsaCheckRuleVO fsaCheckRuleVO);

    /**
     * 根据guid查询单个详情
     */
    @Select("select FSA_CHECK_RULE.*,RSBT_ORG_APPENDIX.TYPE as orgType from FSA_CHECK_RULE,RSBT_ORG,RSBT_ORG_APPENDIX " +
            "where FSA_CHECK_RULE.ORG_GUID = RSBT_ORG.GUID " +
            "AND RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "and FSA_CHECK_RULE.ID = #{id}")
    FsaCheckRuleDTO findOneDto(@Param("id") String id);

    /**
     * 修改全部状态
     */
    @Update("update FSA_CHECK_RULE set STATE = #{state}")
    int updateAllState(@Param("state") String state);

    /**
     * 根据组织机构、制式查询
     */
    @Select("select ORG_GUID,NET_TS,GEN_NUM from FSA_CHECK_RULE where ORG_GUID = #{orgGuid} and state = 1")
    @Results({
            @Result(property = "orgGuid", column = "ORG_GUID"),
            @Result(property = "netTs", column = "NET_TS"),
            @Result(property = "fsaCheckRuleDTOList", column = "{orgGuid=ORG_GUID,netTs=NET_TS}", many = @Many(select = "com.bsm.v4.domain.security.mapper.business.rule.FsaCheckRuleMapper.findAllByOrgGuidAndNetTs"))
    })
    List<FsaCheckRuleDTO> findAllByOrgGuid(@Param("orgGuid") String orgGuid);

    /**
     * 根据组织机构和制式查询
     */
    @Select("select * from FSA_CHECK_RULE where ORG_GUID = #{orgGuid} and NET_TS = #{netTs} and state = 1")
    List<FsaCheckRuleDTO> findAllByOrgGuidAndNetTs(@Param("orgGuid") String orgGuid, @Param("netTs") String netTs);

    /**
     * 入库规则数量查询
     */
    @Select("select count(*) from FSA_CHECK_RULE " +
            "where ORG_GUID = #{fsaCheckRuleVO.orgGuid} and NET_TS = #{fsaCheckRuleVO.netTs} and GEN_NUM = #{fsaCheckRuleVO.genNum} " +
            "and ((FREQ_EFB <= #{fsaCheckRuleVO.freqEfb} and FREQ_EFE >= #{fsaCheckRuleVO.freqEfe}) or (FREQ_RFE <= #{fsaCheckRuleVO.freqRfe} and FREQ_RFB >= #{fsaCheckRuleVO.freqRfb})) " +
            "and (ID != #{fsaCheckRuleVO.id,jdbcType=VARCHAR} or #{fsaCheckRuleVO.id,jdbcType=VARCHAR} is null)")
        int selectCountByOrgAndNetTsAndGenNum(@Param("fsaCheckRuleVO") FsaCheckRuleVO fsaCheckRuleVO);

    /**
     * 根据组织机构查询制式
     */
    @Select("select distinct NET_TS from FSA_CHECK_RULE where ORG_GUID = #{orgGuid} and state = 1")
    List<FsaCheckRuleDTO> findDistinctByOrgGuid(@Param("orgGuid") String orgGuid);

    @Select("select * from FSA_CHECK_RULE where org_type = #{type} and NET_TS = #{netType} ")
    List<FsaCheckRuleDTO> findByOrgGuidAndTechType(@Param("type") String type, @Param("netType") String netType);

    @Select("select * from FSA_CHECK_RULE where org_type = #{orgType} and GEN_NUM = #{genNum}")
    List<FsaCheckRuleDTO> findByOrgGuidAndGenNum(@Param("orgType") String orgType, @Param("genNum") String genNum);

    /**
     * 根据组织机构查询制式和频率规则
     * */
    @Select("select * from FSA_CHECK_RULE where ORG_TYPE = #{orgType} and state = 1")
    List<FsaCheckRuleDTO> findByOrgType(@Param("orgType") String orgType);
}
