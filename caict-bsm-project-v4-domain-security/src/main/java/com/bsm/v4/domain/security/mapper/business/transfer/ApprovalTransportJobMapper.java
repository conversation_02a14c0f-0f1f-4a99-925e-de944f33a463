package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.transfer.ApprovalScheduleDTO;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.bsm.v4.system.model.dto.business.transferNew.TransportJobNewDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.SchedualDataDTO;
import com.bsm.v4.system.model.vo.business.transfer.ApprovalScheduleVO;
import com.bsm.v4.system.model.vo.business.transferNew.TransportJobNewSearchVO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalTransportJob;
import com.bsm.v4.system.model.vo.business.transfer.ApprovalTransportJobVO;
import com.bsm.v4.system.model.vo.business.transferNew.TransportNewJobVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface ApprovalTransportJobMapper extends BasicMapper<ApprovalTransportJob> {

    /**
     * 根据jobGuid查询未提交的数据
     */
    @Select("select * from APPROVAL_TRANSPORT_JOB where JOB_ID = #{jobGuid} and DATA_TYPE = #{dataType} and GEN_NUM = #{genNum} " +
            "and REGION_CODE = #{regionCode} and TECH_TYPE = #{techType} and STATUS = 0")
    ApprovalTransportJobDTO findOneByJobGuidUploadRegion(@Param("jobGuid") String jobGuid, @Param("dataType") String dataType, @Param("genNum") String genNum, @Param("regionCode") String regionCode, @Param("techType") String techType);

//    /**
//     * 分页查询对比任务
//     * */
//    @Select("select APPROVAL_TRANSPORT_JOB.*,SYS_USERS.TYPE as userType from APPROVAL_TRANSPORT_JOB,SYS_USERS where APPROVAL_TRANSPORT_JOB.USER_GUID = SYS_USERS.ID " +
//            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{isCompare,jdbcType=VARCHAR} or #{isCompare,jdbcType=VARCHAR} is null) " +
//            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{appDateStart,jdbcType=VARCHAR} or #{appDateStart,jdbcType=VARCHAR} is null) " +
//            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{appDateEnd,jdbcType=VARCHAR} or #{appDateEnd,jdbcType=VARCHAR} is null) " +
//            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
//            "and (SYS_USERS.TYPE = #{userType,jdbcType=VARCHAR} or #{userType,jdbcType=VARCHAR} is null) " +
//            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE DESC ")
//    List<ApprovalTransportJobDTO> findPageCompareJobVOListByPage(@Param("isCompare")String isCompare, @Param("appDateStart")Date appDateStart,
//                                                                 @Param("appDateEnd") Date appDateEnd, @Param("genNum") String genNum,
//                                                                 @Param("userType") String userType);

    @Select("select count(1) from APPROVAL_TRANSPORT_JOB where JOB_ID = #{jobId} and IS_COMPARE != #{isCompare}")
    int findNotComplete(@Param("jobId") Long jobId, @Param("isCompare") String isCompare);

    @Select({"<script>",
            "select distinct APPROVAL_TRANSPORT_JOB.REGION_CODE from APPROVAL_TRANSPORT_JOB " ,
            "WHERE APPROVAL_TRANSPORT_JOB.org_type = #{usersDTO.type} " +
            "<if test='jobId != null '>" +
            "AND APPROVAL_TRANSPORT_JOB.job_id = #{jobId} " +
            "</if> " +
            "AND APPROVAL_TRANSPORT_JOB.status not IN ",
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>",
            "#{status} ",
            "</foreach>",
            "</script>"})
    List<String> findRegionCodeByStatus(@Param("statusList")List<String> statusList,@Param("usersDTO")UsersDTO usersDTO,@Param("jobId")Long jobId);

    @Select({"<script>",
            "select * from APPROVAL_TRANSPORT_JOB " ,
            "WHERE APPROVAL_TRANSPORT_JOB.org_type = #{usersDTO.type} " +
            "<if test='jobId != null '>" +
            "AND APPROVAL_TRANSPORT_JOB.job_id = #{jobId} " +
            "</if> " +
            "<if test='regionCode != null and regionCode != \"\"'>" +
            "AND APPROVAL_TRANSPORT_JOB.REGION_CODE = #{regionCode} " +
            "</if> " +
            "AND APPROVAL_TRANSPORT_JOB.status not IN ",
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>",
            "#{status} ",
            "</foreach>",
            "</script>"})
    List<ApprovalTransportJob> findRegionCodeByStatusJobId(@Param("statusList")List<String> statusList,@Param("usersDTO")UsersDTO usersDTO,@Param("jobId")Long jobId,@Param("regionCode")String regionCode);

    @Select("select APPROVAL_TRANSPORT_JOB.*,SYS_USERS.TYPE AS userType " +
            "from APPROVAL_TRANSPORT_JOB,SYS_USERS " +
            "where APPROVAL_TRANSPORT_JOB.USER_ID = SYS_USERS.ID " +
            "and (SYS_USERS.TYPE = #{usersDTO.type,jdbcType=VARCHAR} or #{usersDTO.type,jdbcType=VARCHAR} = 'wuwei') " +
            "and (SYS_USERS.TYPE = #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{approvalTransportJobDTO.appDateEnd,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} or #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} or #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} is null) " +
            "and APPROVAL_TRANSPORT_JOB.REGION_CODE in (select code from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE desc ")
    List<ApprovalTransportJobDTO> findListByPage(@Param("approvalTransportJobDTO") ApprovalTransportJobDTO approvalTransportJobDTO, @Param("usersDTO") UsersDTO usersDTO);

    @Select("select count(*) from APPROVAL_TRANSPORT_JOB where JOB_ID = #{jobGuid} and is_compare not in ('18','19')")
    int judgeComplete(@Param("jobGuid") String jobGuid);

    @Select("select count(*) from APPROVAL_TRANSPORT_JOB where region_code = #{regionCode} and USER_ID = #{userGuid} and is_compare not in ('18','19')")
    int judgeCompleteByRegion(@Param("regionCode") String regionCode, @Param("userGuid") String userGuid);

    /**
     * 待办事项job branch查询
     *
     * @param usersDTO                usersDTO
     * @param approvalTransportJobDTO approvalTransportJobDTO
     * @return list
     */
    @Select("select APPROVAL_TRANSPORT_JOB.*,SYS_USERS.TYPE AS userType from APPROVAL_TRANSPORT_JOB,SYS_USERS where APPROVAL_TRANSPORT_JOB.USER_ID = SYS_USERS.ID " +
            "and APPROVAL_TRANSPORT_JOB.JOB_ID =  #{approvalTransportJobDTO.jobGuid,jdbcType=VARCHAR} " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{approvalTransportJobDTO.appDateEnd,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} or #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} or #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} is null) " +
            "and APPROVAL_TRANSPORT_JOB.REGION_CODE in (select code from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE desc ")
    List<ApprovalTransportJobDTO> findDetailListByPage(@Param("approvalTransportJobDTO") ApprovalTransportJobDTO approvalTransportJobDTO, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 办件查询任务列表
     *
     * @param userType userType
     * @return list
     */
    /**
     * 查询条件查询审核否决任务
     */
    @Select("<script>" +
            "SELECT tjn.*,(SELECT COUNT(*) FROM APPROVAL_TRANSPORT_JOB atj WHERE atj.JOB_ID = tjn.ID) AS dataCount " +
            "FROM TRANSPORT_JOB_NEW tjn " +
            "LEFT JOIN SYS_USERS su ON tjn.user_id= su.ID " +
            "WHERE 1=1 " +
            "<if test='jobVO.jobName != null and jobVO.jobName != \"\"'>" +
            "   AND tjn.job_name LIKE CONCAT('%', #{jobVO.jobName}, '%') " +
            "</if>" +
            "<if test='jobVO.status != null and jobVO.status != \"\"'>" +
            "   AND tjn.status = #{jobVO.status,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='jobVO.operatorCode != null and jobVO.operatorCode != \"\"'>" +
            "   AND tjn.operator_code = #{jobVO.operatorCode,jdbcType=VARCHAR} " +
            "</if>" +
            "ORDER BY tjn.created_at DESC LIMIT #{limit} OFFSET #{offset}" +
            "</script>")
    List<TransportJobNewDTO> findJobListByPage(@Param("jobVO") TransportNewJobVO jobVO, @Param("offset") int offset, @Param("limit") int limit);

    @Select("select * from APPROVAL_TRANSPORT_JOB where JOB_ID = #{jobId} " +
            "and (op_user = #{opStatus,jdbcType=VARCHAR} or #{opStatus,jdbcType=VARCHAR} is null ) ")
    List<ApprovalTransportJob> findNotPassList(@Param("jobId") String jobId, @Param("opStatus") String opStatus);

    /**
     * 查询当前任务中有跟上次审核不通过的任务相同的增量、代数、地区的任务
     */
    @Select("select * from APPROVAL_TRANSPORT_JOB where JOB_ID = #{jobId} and data_type = #{dataType} and gen_num = #{genNum} and region_code = #{regionCode} and tech_type = #{techType} ")
    ApprovalTransportJob findByDetail(@Param("jobId") String jobId, @Param("dataType") String dataType, @Param("genNum") String genNum, @Param("regionCode") String regionCode, @Param("techType") String techType);

    @Insert("INSERT INTO APPROVAL_SCHEDULE_LOG (GUID, STATUS, APP_GUID, JOB_ID, APPLICATION_CODE, IS_VALID, CELL_NAME, CELL_ID, BTS_NAME, BTS_ID, TECH_TYPE, " +
            "LOCATION, LONGITUDE, LATITUDE, SEND_START_FREQ, SEND_END_FREQ, ACC_START_FREQ, ACC_END_FREQ, MAX_EMISSIVE_POWER, HEIGHT, DATA_TYPE, COUNTY, IS_HANDLE, " +
            "USER_GUID, VENDOR_NAME, DEVICE_MODEL, MODEL_CODE, ANTENNA_GAIN, GEN_NUM, ANTENNA_MODEL, ANTENNA_FACTORY, POLARIZATION_MODE, ANTENNA_AZIMUTH, FEEDER_LOSS, " +
            "ALTITUDE, UPDATE_TIME, ORG_TYPE, SET_YEAR, SET_MONTH, EXPAND_STATION, ATTRIBUTE_STATION, AT_RANG, AT_EANG, ST_SERV_R, BTS_DATA_TYPE) ( " +
            "select uuid(), STATUS, #{newAppGuid}, JOB_ID, APPLICATION_CODE, IS_VALID, CELL_NAME, CELL_ID, BTS_NAME, BTS_ID, TECH_TYPE, " +
            "LOCATION, LONGITUDE, LATITUDE, SEND_START_FREQ, SEND_END_FREQ, ACC_START_FREQ, ACC_END_FREQ, MAX_EMISSIVE_POWER, HEIGHT, DATA_TYPE, COUNTY, IS_HANDLE, " +
            "USER_GUID, VENDOR_NAME, DEVICE_MODEL, MODEL_CODE, ANTENNA_GAIN, GEN_NUM, ANTENNA_MODEL, ANTENNA_FACTORY, POLARIZATION_MODE, ANTENNA_AZIMUTH, FEEDER_LOSS, " +
            "ALTITUDE, UPDATE_TIME, ORG_TYPE, SET_YEAR, SET_MONTH, EXPAND_STATION, ATTRIBUTE_STATION, AT_RANG, AT_EANG, ST_SERV_R, BTS_DATA_TYPE " +
            "from APPROVAL_SCHEDULE_LOG where APP_GUID = #{oldAppGuid} and BTS_ID in (select distinct ST_C_CODE from RSBT_STATION_BAK where APP_GUID = #{oldAppGuid} and IS_GIVEUP = 2))")
    void insertApprovalScheduleLog(@Param("newAppGuid") String newAppGuid, @Param("oldAppGuid") String oldAppGuid);

    @Select("select * from APPROVAL_TRANSPORT_JOB where JOB_ID = #{jobGuid} and is_compare = #{isCompare}")
    List<ApprovalTransportJob> findByJobGuidAndIsCompare(@Param("jobGuid") Long jobGuid, @Param("isCompare") String isCompare);

    @Select("select * from APPROVAL_TRANSPORT_JOB where JOB_ID = #{jobId} and status = #{status}")
    List<ApprovalTransportJob> findByJobIdAndStatus(@Param("jobId") Long jobId, @Param("status") String status);

    /**
     * 根据jobGuid查询未提交的数据
     */
    @Select("select * from APPROVAL_TRANSPORT_JOB where JOB_ID = #{jobId} and DATA_TYPE = #{dataType} and GEN_NUM = #{genNum} and STATUS = 0")
    ApprovalTransportJobDTO findOneByJobGuidUpload(@Param("jobGuid") String jobId, @Param("dataType") String dataType, @Param("genNum") String genNum);

    /**
     * 查询对比任务总数
     */
    @Select("select count(*) from APPROVAL_TRANSPORT_JOB,SYS_USERS where APPROVAL_TRANSPORT_JOB.USER_ID = SYS_USERS.ID  " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{isCompare,jdbcType=VARCHAR} or #{isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{appDateStart,jdbcType=VARCHAR} or #{appDateStart,jdbcType=VARCHAR} is null) " +
            "and (SYS_USERS.TYPE = #{userType,jdbcType=VARCHAR} or #{userType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{appDateEnd,jdbcType=VARCHAR} or #{appDateStart,jdbcType=VARCHAR} is null) ")
    int selectCountCompareJobVOListByPage(@Param("isCompare") String isCompare, @Param("appDateStart") Date appDateStart, @Param("appDateEnd") Date appDateEnd, @Param("genNum") String genNum, @Param("userType") String userType);

    /**
     * 分页查询对比任务
     */
    @Select("select APPROVAL_TRANSPORT_JOB.*,SYS_USERS.TYPE as userType from APPROVAL_TRANSPORT_JOB,SYS_USERS where APPROVAL_TRANSPORT_JOB.USER_ID = SYS_USERS.ID " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{dto.isCompare,jdbcType=VARCHAR} or #{dto.isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{dto.appDateStart,jdbcType=VARCHAR} or #{dto.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{dto.appDateEnd,jdbcType=VARCHAR} or #{dto.appDateEnd,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{dto.genNum,jdbcType=VARCHAR} or #{dto.genNum,jdbcType=VARCHAR} is null) " +
            "and (SYS_USERS.TYPE = #{dto.userType,jdbcType=VARCHAR} or #{dto.userType,jdbcType=VARCHAR} is null) " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE DESC ")
    List<ApprovalTransportJobDTO> findPageCompareJobVOListByPage(@Param("dto") ApprovalTransportJobDTO dto);

    /**
     * 查询异常数据总数
     */
    @Select("select count(*) from APPROVAL_TRANSPORT_JOB,TRANSPORT_FILE_NEW,SYS_USERS " +
            "where APPROVAL_TRANSPORT_JOB.ID = TRANSPORT_FILE_NEW.job_id and APPROVAL_TRANSPORT_JOB.USER_ID = SYS_USERS.ID " +
            "and (APPROVAL_TRANSPORT_JOB.OP_USER_GUID = #{userId,jdbcType=VARCHAR} or #{userId,jdbcType=VARCHAR} is null) " +
            "and (SYS_USERS.TYPE = #{userType,jdbcType=VARCHAR} or #{userType,jdbcType=VARCHAR} is null) " +
            "and TRANSPORT_FILE_NEW.FILE_STATE = #{fileState} ")
    int selectCountApprovalTransportJoVOLog(@Param("userType") String userType, @Param("userId") String userId, @Param("fileState") Long fileState);

    /**
     * 分页查询异常数据总数
     */
    @Select("select APPROVAL_TRANSPORT_JOB.*,TRANSPORT_FILE_NEW.id as fileGuid,TRANSPORT_FILE_NEW.file_local_name as fileName,TRANSPORT_FILE_NEW.file_state as fileState " +
            "from APPROVAL_TRANSPORT_JOB,TRANSPORT_FILE_NEW,SYS_USERS " +
            "where APPROVAL_TRANSPORT_JOB.ID = TRANSPORT_FILE_NEW.job_id and APPROVAL_TRANSPORT_JOB.USER_ID = SYS_USERS.ID " +
            "and (APPROVAL_TRANSPORT_JOB.OP_USER_GUID = #{userId,jdbcType=VARCHAR} or #{userId,jdbcType=VARCHAR} is null) " +
            "and (SYS_USERS.TYPE = #{userType,jdbcType=VARCHAR} or #{userType,jdbcType=VARCHAR} is null) " +
            "and TRANSPORT_FILE_NEW.FILE_STATE = #{fileState} " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE desc ")
    List<ApprovalTransportJobDTO> findApprovalTransportJobVOLogPage(@Param("userType") String userType, @Param("userId") String userId, @Param("fileState") Integer fileState);

    /**
     * 查询待办任务总数
     */
    @Select("select count(*) from APPROVAL_TRANSPORT_JOB where USER_ID in (select ID from SYS_USERS where (TYPE = #{dto.userType,jdbcType=VARCHAR}) or #{dto.userType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{dto.dataType,jdbcType=VARCHAR} or #{dto.dataType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{dto.genNum,jdbcType=VARCHAR} or #{dto.genNum,jdbcType=VARCHAR} is null) "
    )
    int selectCountByWhere(@Param("dto") ApprovalTransportJobDTO dto);

    /**
     * 分页查询待办任务
     */
    @Select("select atj.*, SYS_REGION.NAME as regionName, TRANSPORT_JOB_NEW.JOB_NAME as jobName from APPROVAL_TRANSPORT_JOB atj,TRANSPORT_JOB_NEW " +
            "left join SYS_REGION on atj.REGION_CODE = SYS_REGION.CODE " +
            "where atj.JOB_ID = TRANSPORT_JOB_NEW.id " +
            "and atj.USER_GUID in (select ID from SYS_USERS where TYPE = #{dto.userType,jdbcType=VARCHAR} or #{dto.userType,jdbcType=VARCHAR} is null) " +
            "and (atj.DATA_TYPE = #{dto.dataType,jdbcType=VARCHAR} or #{dto.dataType,jdbcType=VARCHAR} is null) " +
            "and (atj.GEN_NUM = #{dto.genNum,jdbcType=VARCHAR} or #{dto.genNum,jdbcType=VARCHAR} is null) " +
            "order by atj.GMT_CREATE desc ")
    List<ApprovalTransportJobDTO> findAllPageByWhere(@Param("dto") ApprovalTransportJobDTO dto);

    /**
     * 查看审核列表历史记录总数
     */
    @Select("select count(*) from APPROVAL_TRANSPORT_JOB,TRANSPORT_JOB_NEW,SYS_USERS where TRANSPORT_JOB_NEW.id = APPROVAL_TRANSPORT_JOB.JOB_ID and APPROVAL_TRANSPORT_JOB.USER_GUID = SYS_USERS.ID  " +
            "and (APPROVAL_TRANSPORT_JOB.USER_GUID = #{dto.userGuid,jdbcType=VARCHAR} or #{dto.userGuid,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{dto.appDateStart,jdbcType=VARCHAR} or #{dto.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{dto.appDateEnd,jdbcType=VARCHAR} or #{dto.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB_NEW.JOB_NAME like concat(concat('%',#{dto.jobName,jdbcType=VARCHAR}),'%') or #{dto.jobName,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.STATUS = #{dto.status,jdbcType=VARCHAR} or #{dto.status,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{dto.isCompare,jdbcType=VARCHAR} or #{dto.isCompare,jdbcType=VARCHAR} is null) " +
            "and (SYS_USERS.TYPE = #{dto.userType,jdbcType=VARCHAR} or #{dto.userType,jdbcType=VARCHAR} is null) ")
    int selectCountAppTransportJob(@Param("dto") ApprovalTransportJobDTO dto);

    /**
     * 分页查看审核列表历史记录
     */
    @Select("select APPROVAL_TRANSPORT_JOB.*,TRANSPORT_JOB_NEW.job_name as jobName,SYS_USERS.TYPE as userType " +
            "from APPROVAL_TRANSPORT_JOB,TRANSPORT_JOB_NEW,SYS_USERS " +
            "where TRANSPORT_JOB_NEW.id = APPROVAL_TRANSPORT_JOB.JOB_ID and APPROVAL_TRANSPORT_JOB.USER_GUID = SYS_USERS.ID " +
            "and (APPROVAL_TRANSPORT_JOB.USER_GUID = #{dto.userGuid,jdbcType=VARCHAR} or #{dto.userGuid,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{dto.appDateStart,jdbcType=VARCHAR} or #{dto.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{dto.appDateEnd,jdbcType=VARCHAR} or #{dto.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB_NEW.job_name like concat(concat('%',#{dto.jobName,jdbcType=VARCHAR}),'%') or #{dto.jobName,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.STATUS = #{dto.isApproved,jdbcType=VARCHAR} or #{dto.isApproved,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{dto.isCompare,jdbcType=VARCHAR} or #{dto.isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{dto.dataType,jdbcType=VARCHAR} or #{dto.dataType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{dto.genNum,jdbcType=VARCHAR} or #{dto.genNum,jdbcType=VARCHAR} is null) " +
            "and (SYS_USERS.TYPE = #{dto.userType,jdbcType=VARCHAR} or #{dto.userType,jdbcType=VARCHAR} is null) " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE desc ")
//    @Results({
//            @Result(property = "guid", column = "GUID"),
//            @Result(property = "sectionNum", column = "GUID", one = @One(select = "com.caict.bsm.project.domain.business.mapper.transfer.TransportCompareResultMapper.findCountByAppGuid"))
//    })
    List<ApprovalTransportJobDTO> findAllPageAppTransportJob(@Param("dto") ApprovalTransportJobDTO dto);

    /**
     * 查询条件查询审核否决任务
     */
    @Select("<script>" +
            "SELECT tjn.*, tjn.job_name as jobName, su.NAME as userName " +
            "FROM TRANSPORT_JOB_NEW tjn " +
            "LEFT JOIN SYS_USERS su ON tjn.user_id= su.ID " +
            "WHERE (tjn.user_id = #{userId,jdbcType=VARCHAR} OR #{userId,jdbcType=VARCHAR} IS NULL) " +
            "<if test='jobSearchVO.jobName != null and jobSearchVO.jobName != \"\"'>" +
            "   AND tjn.job_name LIKE CONCAT('%', #{jobSearchVO.jobName}, '%') " +
            "</if>" +
            "<if test='jobSearchVO.status != null and jobSearchVO.status != \"\"'>" +
            "   AND tjn.status = #{jobSearchVO.status,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='jobSearchVO.operatorCode != null and jobSearchVO.operatorCode != \"\"'>" +
            "   AND tjn.operatorCode = #{jobSearchVO.operatorCode,jdbcType=VARCHAR} " +
            "</if>" +
/*            "<if test='jobSearchVO.opUser != null and jobSearchVO.opUser != \"\"'>" +
            "   AND su.NAME LIKE CONCAT('%', #{jobSearchVO.opUser}, '%') " +
            "</if>" +
            "<if test='jobSearchVO.startDate != null'>" +
            "   AND atj.OP_TIME &gt;= #{jobSearchVO.startDate, jdbcType=DATE} " +
            "</if>" +
            "<if test='jobSearchVO.endDate != null'>" +
            "   AND atj.OP_TIME &lt;= #{jobSearchVO.endDate, jdbcType=DATE} " +
            "</if>" +
            "<if test='jobSearchVO.userName != null and jobSearchVO.userName != \"\"'>" +
            "   AND su.NAME LIKE CONCAT('%', #{jobSearchVO.userName}, '%') " +
            "</if>" +*/
            "ORDER BY tjn.created_at DESC" +
            "</script>")
    List<TransportJobNewSearchVO> findApprovalScheduleJobSearchPage(@Param("userId") String userId, @Param("jobSearchVO") TransportNewJobVO jobSearchVO);

    /**
     * 查询审核记录表
     */
    @Select("<script>" +
            "select APPROVAL_SCHEDULE_LOG.* " +
            "from APPROVAL_SCHEDULE_LOG " +
            "where APPROVAL_SCHEDULE_LOG.APP_ID = #{dto.id}" +
            "<if test='dto.status != null and dto.status != \"\"'>" +
            "   AND APPROVAL_SCHEDULE_LOG.STATUS = #{dto.status,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='type == 1'>" +
            "ORDER BY APPROVAL_SCHEDULE_LOG.ID DESC LIMIT #{limit} OFFSET #{offset}" +
            "</if>" +
            "</script>")
    List<ApprovalScheduleLogDTO> findApprovalSchedulePage(@Param("dto") ApprovalTransportJobVO dto,@Param("offset") int offset,@Param("limit") int limit,@Param("type") String type);

    /**
     * 办件查询详情
     */
    @Select("<script>" +
            "select *,APPROVAL_TRANSPORT_JOB.JOB_ID as jobId, APPROVAL_TRANSPORT_JOB.ID,SYS_REGION.NAME as regionName," +
            "(SELECT COUNT(*) FROM APPROVAL_SCHEDULE_LOG atj WHERE atj.APP_ID = APPROVAL_TRANSPORT_JOB.ID) AS dataCount " +
            "from APPROVAL_TRANSPORT_JOB " +
            "left join SYS_REGION on APPROVAL_TRANSPORT_JOB.REGION_CODE = SYS_REGION.CODE " +
            "WHERE JOB_ID = #{jobVO.id,jdbcType=VARCHAR}" +
            "<if test='jobVO.genNum != null and jobVO.genNum != \"\"'>" +
            "   AND APPROVAL_TRANSPORT_JOB.GEN_NUM = #{jobVO.genNum,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='jobVO.dataType != null and jobVO.dataType != \"\"'>" +
            "   AND APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{jobVO.dataType,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='jobVO.techType != null and jobVO.techType != \"\"'>" +
            "   AND APPROVAL_TRANSPORT_JOB.TECH_TYPE = #{jobVO.techType,jdbcType=VARCHAR} " +
            "</if>" +
            "ORDER BY APPROVAL_TRANSPORT_JOB.GMT_CREATE DESC LIMIT #{limit} OFFSET #{offset}" +
            "</script>")
    List<ApprovalTransportJobDTO> findDetailByPage(@Param("jobVO") ApprovalTransportJobVO jobVO, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 办件查询详情
     */
    @Select("select ID,APP_ID,JOB_ID,BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,GEN_NUM,ORG_TYPE" +
            " from APPROVAL_SCHEDULE where APP_ID=  #{jobVO.id} " +
            " group by BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,GEN_NUM,ORG_TYPE " +
            " order by BTS_ID DESC LIMIT #{limit} OFFSET #{offset}")
    List<ApprovalScheduleDTO> findPendingDetailByWhere(@Param("jobVO") ApprovalTransportJobVO jobVO, @Param("offset") int offset, @Param("limit") int limit);

    @Select("select * " +
            " from APPROVAL_SCHEDULE " +
            "where 1 = 1 " +
            "and (BTS_ID = #{vo.btsId,jdbcType=VARCHAR} or #{vo.btsId,jdbcType=VARCHAR} is null) " +
            "and (BTS_NAME = #{vo.btsName,jdbcType=VARCHAR} or #{vo.btsName,jdbcType=VARCHAR} is null) " +
            "and (TECH_TYPE = #{vo.techType,jdbcType=VARCHAR} or #{vo.techType,jdbcType=VARCHAR} is null) " +
            "and (LOCATION = #{vo.location,jdbcType=VARCHAR} or #{vo.location,jdbcType=VARCHAR} is null) " +
            "and (COUNTY = #{vo.county,jdbcType=VARCHAR} or #{vo.county,jdbcType=VARCHAR} is null) " +
            "and (ALTITUDE = #{vo.altitude,jdbcType=VARCHAR} or #{vo.altitude,jdbcType=VARCHAR} is null) " +
            "and (LATITUDE = #{vo.latitude,jdbcType=VARCHAR} or #{vo.latitude,jdbcType=VARCHAR} is null) " +
            "and (LONGITUDE = #{vo.longitude,jdbcType=VARCHAR} or #{vo.longitude,jdbcType=VARCHAR} is null) " +
            "and (GEN_NUM = #{vo.genNum,jdbcType=VARCHAR} or #{vo.genNum,jdbcType=VARCHAR} is null) " +
            "and (ORG_TYPE = #{vo.orgType,jdbcType=VARCHAR} or #{vo.orgType,jdbcType=VARCHAR} is null) " +
            " order by CELL_ID DESC LIMIT #{limit} OFFSET #{offset}")
    List<ApprovalScheduleDTO> findCellDetail(@Param("vo") ApprovalScheduleVO vo, @Param("offset") int offset, @Param("limit") int limit);

    @Select("<script>" +
            "SELECT COUNT(*)" +
            "FROM TRANSPORT_JOB_NEW tjn " +
            "LEFT JOIN SYS_USERS su ON tjn.user_id= su.ID " +
            "WHERE 1=1 " +
            "<if test='jobVO.jobName != null and jobVO.jobName != \"\"'>" +
            "   AND tjn.job_name LIKE CONCAT('%', #{jobVO.jobName}, '%') " +
            "</if>" +
            "<if test='jobVO.status != null and jobVO.status != \"\"'>" +
            "   AND tjn.status = #{jobVO.status,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='jobVO.operatorCode != null and jobVO.operatorCode != \"\"'>" +
            "   AND tjn.operator_code = #{jobVO.operatorCode,jdbcType=VARCHAR} " +
            "</if>" +
            "ORDER BY tjn.created_at DESC" +
            "</script>")
    long countByJobId(@Param("jobVO") TransportNewJobVO jobVO);

    /**
     * 办件查询详情
     */
    @Select("<script>" +
            "select COUNT(*) " +
            "from APPROVAL_TRANSPORT_JOB " +
            "left join SYS_REGION on APPROVAL_TRANSPORT_JOB.REGION_CODE = SYS_REGION.CODE " +
            "WHERE JOB_ID = #{jobVO.id,jdbcType=VARCHAR}" +
            "<if test='jobVO.genNum != null and jobVO.genNum != \"\"'>" +
            "   AND APPROVAL_TRANSPORT_JOB.GEN_NUM = #{jobVO.genNum,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='jobVO.dataType != null and jobVO.dataType != \"\"'>" +
            "   AND APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{jobVO.dataType,jdbcType=VARCHAR} " +
            "</if>" +
            "</script>")
    long countByJobDetail(@Param("jobVO") ApprovalTransportJobVO jobVO);

    /**
     * 查询审核记录表
     */
    @Select("<script>" +
            "select COUNT(*) " +
            "from APPROVAL_SCHEDULE_LOG " +
            "where APPROVAL_SCHEDULE_LOG.APP_ID = #{dto.id}" +
            "<if test='dto.status != null and dto.status != \"\"'>" +
            "   AND APPROVAL_SCHEDULE_LOG.STATUS = #{dto.status,jdbcType=VARCHAR} " +
            "</if>" +
            "</script>")
    long countByApprovalScheduleId(@Param("dto") ApprovalTransportJobVO dto);

    @Select("select COUNT(*) FROM (" +
            "select ID,APP_ID,JOB_ID,BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,GEN_NUM " +
            "from APPROVAL_SCHEDULE where APP_ID=  #{jobVO.id} " +
            "group by BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,GEN_NUM,ORG_TYPE ) as count")
    long countPendingDetail(@Param("jobVO") ApprovalTransportJobVO jobVO);

    @Select("select COUNT(*) FROM" +
            "(select * from APPROVAL_SCHEDULE " +
            "where 1=1 " +
            "and (BTS_ID = #{vo.btsId,jdbcType=VARCHAR} or #{vo.btsId,jdbcType=VARCHAR} is null) " +
            "and (BTS_NAME = #{vo.btsName,jdbcType=VARCHAR} or #{vo.btsName,jdbcType=VARCHAR} is null) " +
            "and (TECH_TYPE = #{vo.techType,jdbcType=VARCHAR} or #{vo.techType,jdbcType=VARCHAR} is null) " +
            "and (LOCATION = #{vo.location,jdbcType=VARCHAR} or #{vo.location,jdbcType=VARCHAR} is null) " +
            "and (COUNTY = #{vo.county,jdbcType=VARCHAR} or #{vo.county,jdbcType=VARCHAR} is null) " +
            "and (ALTITUDE = #{vo.altitude,jdbcType=VARCHAR} or #{vo.altitude,jdbcType=VARCHAR} is null) " +
            "and (LATITUDE = #{vo.latitude,jdbcType=VARCHAR} or #{vo.latitude,jdbcType=VARCHAR} is null) " +
            "and (LONGITUDE = #{vo.longitude,jdbcType=VARCHAR} or #{vo.longitude,jdbcType=VARCHAR} is null) " +
            "and (GEN_NUM = #{vo.genNum,jdbcType=VARCHAR} or #{vo.genNum,jdbcType=VARCHAR} is null) " +
            "and (ORG_TYPE = #{vo.orgType,jdbcType=VARCHAR} or #{vo.orgType,jdbcType=VARCHAR} is null) " +
            " order by CELL_ID ) as count")
    long countCellDetail(@Param("vo") ApprovalScheduleVO vo);

    /**
     * 办件查询详情
     * 未group up查询
     */
    @Select("select *" +
            " from APPROVAL_SCHEDULE where APP_ID=  #{jobVO.id} " +
            " order by BTS_ID")
    List<ApprovalScheduleDTO> findPendingDetailPage(@Param("jobVO")ApprovalTransportJobVO jobVO);

    /**
     * 代办查询
     *
     * @param regionId regionId
     * @return json
     */
    @Select("SELECT APPROVAL_TRANSPORT_JOB.ID AS appId, APPROVAL_TRANSPORT_JOB.DATA_TYPE AS dataType, " +
            "TRANSPORT_JOB_NEW.job_name AS jobName, APPROVAL_TRANSPORT_JOB.GEN_NUM AS genNum, " +
            "APPROVAL_TRANSPORT_JOB.TECH_TYPE AS techType, APPROVAL_TRANSPORT_JOB.ORG_TYPE AS orgType, " +
            "APPROVAL_TRANSPORT_JOB.STATION_COUNT AS num, APPROVAL_TRANSPORT_JOB.REGION_NAME AS area " +
            "FROM APPROVAL_TRANSPORT_JOB LEFT JOIN TRANSPORT_JOB_NEW ON TRANSPORT_JOB_NEW.id = APPROVAL_TRANSPORT_JOB.JOB_ID " +
            "WHERE APPROVAL_TRANSPORT_JOB.REGION_CODE = #{regionId,jdbcType=VARCHAR} AND APPROVAL_TRANSPORT_JOB.STATUS = 'approving' " +
            "ORDER BY APPROVAL_TRANSPORT_JOB.REGION_CODE")
    List<SchedualDataDTO> findSchedual(@Param("regionId") String regionId);

    @Select("SELECT APPROVAL_TRANSPORT_JOB.ID AS appId, APPROVAL_TRANSPORT_JOB.DATA_TYPE AS dataType, " +
            "TRANSPORT_JOB_NEW.job_name AS jobName, APPROVAL_TRANSPORT_JOB.GEN_NUM AS genNum, " +
            "APPROVAL_TRANSPORT_JOB.TECH_TYPE AS techType, APPROVAL_TRANSPORT_JOB.ORG_TYPE AS orgType, " +
            "APPROVAL_TRANSPORT_JOB.STATION_COUNT AS num, APPROVAL_TRANSPORT_JOB.REGION_NAME AS area " +
            "FROM APPROVAL_TRANSPORT_JOB LEFT JOIN TRANSPORT_JOB_NEW ON TRANSPORT_JOB_NEW.id = APPROVAL_TRANSPORT_JOB.JOB_ID " +
            "WHERE APPROVAL_TRANSPORT_JOB.STATUS = 'approving' ORDER BY APPROVAL_TRANSPORT_JOB.REGION_CODE")
    List<SchedualDataDTO> findSchedualP(@Param("regionId")String regionId);
}
