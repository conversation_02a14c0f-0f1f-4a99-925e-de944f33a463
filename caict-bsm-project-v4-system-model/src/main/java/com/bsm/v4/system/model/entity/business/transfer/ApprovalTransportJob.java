package com.bsm.v4.system.model.entity.business.transfer;

import com.caictframework.model.entity.BasicEntity;
import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-01
 */

@TableName("APPROVAL_TRANSPORT_JOB")
public class ApprovalTransportJob extends BasicEntity<Long> {

    @TableFieId("GMT_CREATE")
    private Date gmtCreate;
    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;
    @TableFieId("IS_DELETED")
    private Long isDeleted;
    /**
     * 基站GUID
     */
    @TableFieId("JOB_ID")
    private Long jobId;
    /**
     * 审批状态
     */
    @TableFieId("STATUS")
    private String status;
    /**
     * 申请条件
     */
    @TableFieId("UNIT_APPROVED")
    private Long unitApproved;
    /**
     * 审核材料
     */
    @TableFieId("MATERIAL_APPROVED")
    private Long materialApproved;
    /**
     * user guid
     */
    @TableFieId("OP_USER")
    private String opUser;
    /**
     * user guid
     */
    @TableFieId("OP_USER_GUID")
    private String opUserGuid;
    /**
     * 审核意见
     */
    @TableFieId("OP_DETAIL")
    private String opDetail;
    /**
     * 审核时间
     */
    @TableFieId("OP_TIME")
    private Date opTime;

    @TableFieId("IS_COMPARE")
    private String isCompare;

    //对比差异数量
    @TableFieId("DIFF_COUNT")
    private Long diffCount;

    @TableFieId("JOB_CODE")
    private String jobCode;

    //对比无差异数量
    @TableFieId("IDEN_COUNT")
    private Long idenCount;

    //代数
    @TableFieId("GEN_NUM")
    private String genNum;
    //数据类型
    @TableFieId("DATA_TYPE")
    private String dataType;
    //地区
    @TableFieId("REGION_CODE")
    private String regionCode;

    //地区名称
    @TableFieId("REGION_NAME")
    private String regionName;

    //申请表编号
    @TableFieId("APP_CODE")
    private String appCode;

    //制式
	@TableFieId("TECH_TYPE")
	private String techType;

    //制式
    @TableFieId("ORG_TYPE")
    private String orgType;

    //制式
    @TableFieId("STATION_COUNT")
    private Integer stationCount;

    public ApprovalTransportJob() {
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public Integer getStationCount() {
        return stationCount;
    }

    public void setStationCount(Integer stationCount) {
        this.stationCount = stationCount;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getUnitApproved() {
        return unitApproved;
    }

    public void setUnitApproved(Long unitApproved) {
        this.unitApproved = unitApproved;
    }

    public Long getMaterialApproved() {
        return materialApproved;
    }

    public void setMaterialApproved(Long materialApproved) {
        this.materialApproved = materialApproved;
    }

    public String getOpUser() {
        return opUser;
    }

    public void setOpUser(String opUser) {
        this.opUser = opUser;
    }

    public String getOpUserGuid() {
        return opUserGuid;
    }

    public void setOpUserGuid(String opUserGuid) {
        this.opUserGuid = opUserGuid;
    }

    public String getOpDetail() {
        return opDetail;
    }

    public void setOpDetail(String opDetail) {
        this.opDetail = opDetail;
    }

    public Date getOpTime() {
        return opTime;
    }

    public void setOpTime(Date opTime) {
        this.opTime = opTime;
    }

    public String getIsCompare() {
        return isCompare;
    }

    public void setIsCompare(String isCompare) {
        this.isCompare = isCompare;
    }

    public Long getDiffCount() {
        return diffCount;
    }

    public void setDiffCount(Long diffCount) {
        this.diffCount = diffCount;
    }

    public String getJobCode() {
        return jobCode;
    }

    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }

    public Long getIdenCount() {
        return idenCount;
    }

    public void setIdenCount(Long idenCount) {
        this.idenCount = idenCount;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }
}
