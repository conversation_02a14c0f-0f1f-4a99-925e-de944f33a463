package com.bsm.v4.system.model.contrust.system;

/**
 * <AUTHOR>
 * @date 2024/11/12 9:32
 * @description:
 */
public enum ResponseCodeConst {

    NORMAL("200", "正常状态"),
    SUCCESS("200", "操作成功"),
    BAD_REQUEST("400", "请求参数错误"),
    UNAUTHORIZED("401", "未授权访问"),
    FORBIDDEN("403", "禁止访问"),
    NOT_FOUND("404", "资源不存在"),
    CONFLICT("409", "数据冲突"),
    BUG("500", "程序异常"),
    TIMEOUT("600", "登录超时"),
    WARNING("700", "警告信息"),
    PROCESSING("202", "处理中"),
    INVALID_DATA("422", "数据校验失败");

    private final String type;
    private final String name;

    ResponseCodeConst(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

}
